import { KeepTrackApiEvents, ToastMsgType } from '@app/interfaces';
import { SatInfoBox } from '@app/plugins/select-sat-manager/sat-info-box';
import { SelectSatManager } from '@app/plugins/select-sat-manager/select-sat-manager';
import type { CatalogManager } from '@app/singletons/catalog-manager';
import { GroupType, ObjectGroup } from '@app/singletons/object-group';
import { DetailedSatellite, SpaceObjectType, Star } from 'ootk';
import { InputEventType, keepTrackApi } from '../keepTrackApi';
import { getEl } from '../lib/get-el';
import { slideInDown } from '../lib/slide';
import { TopMenu } from '../plugins/top-menu/top-menu';
import { MissileObject } from './catalog-manager/MissileObject';
import { errorManagerInstance } from './errorManager';
import type { UiManager } from './uiManager';

export interface SearchResult {
  id: number; // Catalog Index
  searchType: SearchResultType; // Type of search result
  strIndex: number; // Index of the search string in the name
  patlen: number; // Length of the search string
}

export enum SearchResultType {
  BUS,
  OBJECT_NAME,
  ALT_NAME,
  NORAD_ID,
  INTLDES,
  LAUNCH_VEHICLE,
  MISSILE,
  STAR,
}

/**
 * The `SearchManager` class is responsible for managing the search functionality in the UI.
 * It provides methods for performing searches, hiding search results, and retrieving information
 * about the current search state.
 */
export class SearchManager {
  isSearchOpen = false;
  isResultsOpen = false;
  private lastResultGroup_: ObjectGroup<GroupType> | null = null;
  private uiManager_: UiManager;

  constructor(uiManager: UiManager) {
    this.uiManager_ = uiManager;
    const uiWrapper = getEl('ui-wrapper');
    const searchResults = document.createElement('div');

    searchResults.id = TopMenu.SEARCH_RESULT_ID;
    // 不设置内联样式，让CSS样式在加载完成后生效

    uiWrapper!.prepend(searchResults);

    keepTrackApi.on(KeepTrackApiEvents.uiManagerFinal, () => {
      this.addListeners_();
      // 🔥 在uiManagerFinal事件中也尝试应用样式，作为双重保险
      this.applySearchResultsStyles_();
    });
  }

  /**
   * 应用搜索结果框的CSS样式
   * 解决CSS异步加载导致的样式不生效问题
   */
  private applySearchResultsStyles_(): void {
    const searchResults = getEl('search-results');
    if (searchResults) {
      // 强制重新计算样式，让CSS规则生效
      searchResults.offsetHeight;
      console.log('🎯 SearchManager: 搜索结果框样式已重新应用');
    }
  }

  init() {
    keepTrackApi.on(InputEventType.KeyDown, (key: string, _code: string, isRepeat: boolean) => {
      if (key === 'F' && !isRepeat) {
        this.toggleSearch();
        if (this.isSearchOpen) {
          setTimeout(() => {
            getEl('search')?.focus();
          }, 1000);
        }
      }
    });
  }

  private addListeners_() {
    getEl('search-results')?.addEventListener('click', (evt: Event) => {
      const satId = SearchManager.getSatIdFromSearchResults_(evt);

      if (isNaN(satId) || satId === -1) {
        return;
      }

      const catalogManagerInstance = keepTrackApi.getCatalogManager();
      const obj = catalogManagerInstance.getObject(satId);

      if (obj?.type === SpaceObjectType.STAR) {
        keepTrackApi.getMainCamera().lookAtStar(obj as Star);
      } else {
        keepTrackApi.getPlugin(SelectSatManager)?.selectSat(satId);
      }
    });
    getEl('search-results')?.addEventListener('mouseover', (evt) => {
      const satId = SearchManager.getSatIdFromSearchResults_(evt);

      if (isNaN(satId) || satId === -1) {
        return;
      }

      keepTrackApi.getHoverManager().setHoverId(satId);
      this.uiManager_.searchHoverSatId = satId;
    });
    getEl('search-results')?.addEventListener('mouseout', () => {
      keepTrackApi.getHoverManager().setHoverId(-1);
      this.uiManager_.searchHoverSatId = -1;
    });
    // 🔥 优化：添加防抖，避免频繁搜索
    let searchTimeout: number | null = null;
    getEl('search')?.addEventListener('input', (e) => {
      const searchStr = (e.target as HTMLInputElement).value;

      // 🔥 清除之前的搜索定时器
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }

      // 🔥 空字符串立即执行，其他搜索防抖300ms
      if (searchStr === '') {
        this.doSearch(searchStr);
      } else {
        searchTimeout = setTimeout(() => {
          this.doSearch(searchStr);
          searchTimeout = null;
        }, 300) as any;
      }
    });

    getEl('search')?.addEventListener('blur', () => {
      if (this.isSearchOpen && this.getCurrentSearch().length === 0) {
        this.hideResults();
        this.toggleSearch();
      }
    });
    getEl('search-icon')?.addEventListener('click', () => {
      this.toggleSearch();
    });

    // 🔥 优化：添加防抖，避免频繁触发resize事件
    let resizeTimeout: number | null = null;
    window.addEventListener('resize', () => {
      if (!this.isResultsOpen) return; // 🔥 早期返回，避免不必要的处理

      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }

      resizeTimeout = setTimeout(() => {
        this.adjustSearchResultsHeight_();
        resizeTimeout = null;
      }, 150) as any; // 🔥 防抖150ms
    });
  }

  private static getSatIdFromSearchResults_(evt: Event) {
    let satId = -1;

    if ((<HTMLElement>evt.target).classList.contains('search-result')) {
      const satIdStr = (<HTMLElement>evt.target).dataset.objId;

      satId = satIdStr ? parseInt(satIdStr) : -1;
    } else if ((<HTMLElement>evt.target).parentElement?.classList.contains('search-result')) {
      const satIdStr = (<HTMLElement>evt.target).parentElement?.dataset.objId;

      satId = satIdStr ? parseInt(satIdStr) : -1;
    } else if ((<HTMLElement>evt.target).parentElement?.parentElement?.classList.contains('search-result')) {
      const satIdStr = (<HTMLElement>evt.target).parentElement?.parentElement?.dataset.objId;

      satId = satIdStr ? parseInt(satIdStr) : -1;
    }

    return satId;
  }

  /**
   * Returns a boolean indicating whether the search results box is currently open.
   */
  getLastResultGroup(): ObjectGroup<GroupType> | null {
    return this.lastResultGroup_;
  }

  /**
   * Returns the current search string entered by the user.
   */
  getCurrentSearch(): string {
    if (this.isResultsOpen) {
      const searchDom = <HTMLInputElement>getEl('search', true);

      if (searchDom) {
        return searchDom.value;
      }
    }

    return '';
  }

  /**
   * Hides the search results box and clears the selected satellite group.
   * Restores display of all satellites by disabling group color scheme.
   */
  hideResults(): void {
    try {
      const catalogManagerInstance = keepTrackApi.getCatalogManager();
      const dotsManagerInstance = keepTrackApi.getDotsManager();
      const groupManagerInstance = keepTrackApi.getGroupsManager();
      const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();
      const orbitManagerInstance = keepTrackApi.getOrbitManager();

      // 隐藏搜索结果框
      const resultsEl = getEl('search-results');
      if (resultsEl) {
        resultsEl.style.display = 'none';
      }

      // 清除搜索结果轨道
      if (this.lastResultGroup_ && this.lastResultGroup_.ids) {
        this.lastResultGroup_.ids.forEach((satId: number) => {
          orbitManagerInstance.removeInViewOrbit(satId);
        });
      }

      // 清除群组选择，恢复正常显示
      groupManagerInstance.clearSelect();
      this.isResultsOpen = false;
      this.lastResultGroup_ = null;

      // 清除搜索状态
      settingsManager.lastSearch = '';
      settingsManager.lastSearchResults = [];
      dotsManagerInstance.updateSizeBuffer(catalogManagerInstance.objectCache.length);

      // 🔧 禁用群组颜色方案，恢复显示所有卫星
      colorSchemeManagerInstance.isUseGroupColorScheme = false;
      colorSchemeManagerInstance.calculateColorBuffers(true);




    } catch (error) {
      errorManagerInstance.log(error);
    }
  }

  static doArraySearch(catalogManagerInstance: CatalogManager, array: number[]) {
    return array.reduce((searchStr, i) => {
      const detailedSatellite = <DetailedSatellite>catalogManagerInstance.objectCache[i];

      // Use the sccNum unless it is missing (Vimpel), then use name
      return detailedSatellite?.sccNum.length > 0 ? `${searchStr}${detailedSatellite.sccNum},` : `${searchStr}${detailedSatellite.name},`;
    }, '').slice(0, -1);
  }

  doSearch(searchString: string, isPreventDropDown?: boolean): void {
    // 搜索字符串为空时，恢复显示所有卫星
    if (searchString === '' || searchString.length === 0) {
      this.hideResults();
      keepTrackApi.emit(KeepTrackApiEvents.searchUpdated, searchString);
      // 确保搜索结果框完全隐藏
      getEl('search-results')!.style.display = 'none';
      return;
    }

    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const dotsManagerInstance = keepTrackApi.getDotsManager();

    if (catalogManagerInstance.objectCache.length === 0) {
      throw new Error('No sat data loaded! Check if TLEs are corrupted!');
    }

    const searchDom = <HTMLInputElement>getEl('search');

    if (searchDom) {
      searchDom.value = searchString;
    }

    /*
     * Don't search for things until at least the minimum characters
     * are typed otherwise there are just too many search results.
     */
    if (searchString.length <= settingsManager.minimumSearchCharacters && searchString !== 'RV_') {
      return;
    }

    // Emit the search before it is modified
    keepTrackApi.emit(KeepTrackApiEvents.searchUpdated, searchString);

    // Uppercase to make this search not case sensitive
    searchString = searchString.toUpperCase();

    /*
     * NOTE: We are no longer using spaces because it is difficult
     * to predict when a space is part of satellite name.
     */

    /*
     * Split string into array using comma or space as delimiter
     * let searchList = searchString.split(/(?![0-9]+)\s(?=[0-9]+)|,/u);
     */

    let results = <SearchResult[]>[];
    // If so, then do a number only search

    if ((/^[0-9,，]+$/u).test(searchString)) {
      results = SearchManager.doNumOnlySearch_(searchString);
    } else {
      // If not, then do a regular search
      results = SearchManager.doRegularSearch_(searchString);
    }

    // Remove any results greater than the maximum allowed
    results = results.splice(0, settingsManager.searchLimit);

    // Make a group to hilight results
    const idList = results.map((sat) => sat.id);

    settingsManager.lastSearchResults = idList;

    dotsManagerInstance.updateSizeBuffer(catalogManagerInstance.objectCache.length);

    const groupManagerInstance = keepTrackApi.getGroupsManager();
    const uiManagerInstance = keepTrackApi.getUiManager();

    const dispGroup = groupManagerInstance.createGroup(GroupType.ID_LIST, idList);

    this.lastResultGroup_ = dispGroup;

    if (!isPreventDropDown && idList.length > 0) {
      // 先设置搜索结果状态，确保getCurrentSearch()能正确返回搜索内容
      this.isResultsOpen = true;

      // 选择群组（这会自动启用群组颜色方案并重新计算颜色缓冲区）
      groupManagerInstance.selectGroup(dispGroup);

      // 确保颜色方案管理器使用群组颜色方案并强制重新计算
      const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();
      colorSchemeManagerInstance.setToGroupColorScheme();

      // 立即强制重新计算颜色缓冲区以确保搜索结果正确显示
      colorSchemeManagerInstance.calculateColorBuffers(true);

      // 显示搜索结果框，只设置显示状态，让CSS处理样式
      const searchResultsEl = getEl('search-results')!;
      searchResultsEl.style.display = 'block';

      this.fillResultBox(results, catalogManagerInstance);
    }

    if (idList.length === 0) {
      if (settingsManager.lastSearch?.length > settingsManager.minimumSearchCharacters) {
        uiManagerInstance.toast('No Results Found', ToastMsgType.serious, false);
      }
      this.hideResults();
    }
  }

  private static doRegularSearch_(searchString: string) {
    const results: SearchResult[] = [];

    // Split string into array using comma (support both English and Chinese comma)
    const searchList = searchString.split(/[,，]/u);

    // Update last search with the most recent search results
    settingsManager.lastSearch = searchList;

    // Initialize search results
    const satData = SearchManager.getSearchableObjects_(true) as (DetailedSatellite & MissileObject)[];

    searchList.forEach((searchStringIn) => {
      keepTrackApi.analytics.track('search', {
        search: searchStringIn,
      });

      satData.every((sat) => {
        if (results.length >= settingsManager.searchLimit) {
          return false;
        }
        const len = searchStringIn.length;

        if (len === 0) {
          return true;
        } // Skip empty strings
        // TODO: #855 Allow searching for other types of objects
        if (!sat.isMissile() && !sat.isSatellite()) {
          return true;
        } // Skip non satellites and missiles

        // Skip debris in search results regardless of layer settings
        if (sat.type === SpaceObjectType.DEBRIS || (sat.type as any) === 3) {
          return true;
        }

        // TODO: Vimpel additions may slow things down - perhaps make it a setting?
        if ((sat.name.toUpperCase().indexOf(searchStringIn) !== -1 && !sat.name.includes('Vimpel'))) { // || sat.name.toUpperCase() === searchStringIn) {
          results.push({
            strIndex: sat.name.indexOf(searchStringIn),
            searchType: SearchResultType.OBJECT_NAME,
            patlen: len,
            id: sat.id,
          });

          return true; // Prevent's duplicate results
        }

        if (sat.altName && sat.altName.toUpperCase().indexOf(searchStringIn) !== -1) {
          results.push({
            strIndex: sat.altName.toUpperCase().indexOf(searchStringIn),
            searchType: SearchResultType.ALT_NAME,
            patlen: len,
            id: sat.id,
          });

          return true; // Prevent's duplicate results
        }

        if (typeof sat.bus !== 'undefined' && sat.bus.toUpperCase().indexOf(searchStringIn) !== -1) {
          results.push({
            strIndex: sat.bus.toUpperCase().indexOf(searchStringIn),
            searchType: SearchResultType.BUS,
            patlen: len,
            id: sat.id,
          });

          return true; // Prevent's duplicate results
        }

        if (!sat.desc) {
          // Do nothing there is no description property
        } else if (sat.desc.toUpperCase().indexOf(searchStringIn) !== -1) {
          results.push({
            strIndex: sat.desc.toUpperCase().indexOf(searchStringIn),
            searchType: SearchResultType.MISSILE,
            patlen: len,
            id: sat.id,
          });

          return true; // Prevent's duplicate results
        } else {
          return true; // Last check for missiles
        }

        if (sat.sccNum && sat.sccNum.indexOf(searchStringIn) !== -1) {
          // Ignore Notional Satellites unless all 6 characters are entered
          if (sat.name.includes(' Notional)') && searchStringIn.length < 6) {
            return true;
          }

          results.push({
            strIndex: sat.sccNum.indexOf(searchStringIn),
            searchType: SearchResultType.NORAD_ID,
            patlen: len,
            id: sat.id,
          });

          return true; // Prevent's duplicate results
        }

        if (sat.intlDes && sat.intlDes.indexOf(searchStringIn) !== -1 && !sat.name.includes('Vimpel')) {
          // Ignore Notional Satellites
          if (sat.name.includes(' Notional)')) {
            return true;
          }

          results.push({
            strIndex: sat.intlDes.indexOf(searchStringIn),
            searchType: SearchResultType.INTLDES,
            patlen: len,
            id: sat.id,
          });

          return true; // Prevent's duplicate results
        }

        if (sat.launchVehicle && sat.launchVehicle.toUpperCase().indexOf(searchStringIn) !== -1) {
          results.push({
            strIndex: sat.launchVehicle.toUpperCase().indexOf(searchStringIn),
            searchType: SearchResultType.LAUNCH_VEHICLE,
            patlen: len,
            id: sat.id,
          });

          return true; // Prevent's duplicate results
        }

        return true;
      });
    });

    return results;
  }

  private static doNumOnlySearch_(searchString: string) {
    let results: SearchResult[] = [];

    // Split string into array using comma (support both English and Chinese comma)
    let searchList = searchString.split(/[,，]/u).filter((str) => str.length > 0);
    // Sort the numbers so that the lowest numbers are searched first

    searchList = searchList.sort((a, b) => parseInt(a) - parseInt(b));

    // Update last search with the most recent search results
    settingsManager.lastSearch = searchList;

    // Initialize search results
    const satData = (SearchManager.getSearchableObjects_(false) as DetailedSatellite[]).sort((a, b) => parseInt(a.sccNum6) - parseInt(b.sccNum6));

    let i = 0;
    let lastFoundI = 0;

    searchList.forEach((searchStringIn) => {
      // Don't search for things until at least the minimum characters
      if (searchStringIn.length <= settingsManager.minimumSearchCharacters) {
        return;
      }
      // Last one never got found
      if (i >= satData.length) {
        i = lastFoundI;
      }

      keepTrackApi.analytics.track('search', {
        search: searchStringIn,
      });

      for (; i < satData.length; i++) {
        if (results.length >= settingsManager.searchLimit) {
          break;
        }

        const sat = satData[i];
        // Ignore Notional Satellites unless all 6 characters are entered

        if (sat.type === SpaceObjectType.NOTIONAL && searchStringIn.length < 6) {
          continue;
        }

        // Skip debris in search results regardless of layer settings
        if (sat.type === SpaceObjectType.DEBRIS || (sat.type as any) === 3) {
          console.log('Skipping debris in number search:', sat.name, 'type:', sat.type);
          continue;
        }

        // Check if matches 6Digit
        if (sat.sccNum6 && sat.sccNum6.indexOf(searchStringIn) !== -1) {
          results.push({
            strIndex: sat.sccNum.indexOf(searchStringIn),
            patlen: searchStringIn.length,
            id: sat.id,
            searchType: SearchResultType.NORAD_ID,
          });
          lastFoundI = i;

          if (searchStringIn.length === 6) {
            break;
          }
        }
      }
    });

    // Remove any duplicates in results
    results = results.filter((result, index, self) => index === self.findIndex((t) => t.id === result.id));

    return results;
  }

  private static getSearchableObjects_(isIncludeMissiles = true): (DetailedSatellite | MissileObject)[] | DetailedSatellite[] {
    const catalogManagerInstance = keepTrackApi.getCatalogManager();
    const searchableObjects = (
      catalogManagerInstance.objectCache.filter((obj) => {
        if (obj.isSensor() || obj.isMarker() || obj.isGroundObject() || obj.isStar()) {
          return false;
        } // Skip static dots (Maybe these should be searchable?)
        if (!isIncludeMissiles && obj.isMissile()) {
          return false;
        } // Skip missiles (if not searching for missiles

        // Skip debris in search results regardless of layer settings
        if (obj.type === SpaceObjectType.DEBRIS || (obj.type as any) === 3) {
          return false;
        }

        if (!(obj as MissileObject).active) {
          return false;
        } // Skip inactive missiles.
        if ((obj as DetailedSatellite).country === 'ANALSAT' && !obj.active) {
          return false;
        } // Skip Fake Analyst satellites
        if (!obj.name) {
          return false;
        } // Everything has a name. If it doesn't then assume it isn't what we are searching for.

        return true;
      }) as (DetailedSatellite & MissileObject)[]
    ).sort((a, b) => {
      // Sort by sccNum
      if ((a as DetailedSatellite).sccNum && (b as DetailedSatellite).sccNum) {
        return parseInt((a as DetailedSatellite).sccNum) - parseInt((b as DetailedSatellite).sccNum);
      }

      return 0;

    });

    return isIncludeMissiles ? (searchableObjects as (DetailedSatellite | MissileObject)[]) : (searchableObjects as DetailedSatellite[]);
  }

  fillResultBox(results: SearchResult[], catalogManagerInstance: CatalogManager) {
    const colorSchemeManagerInstance = keepTrackApi.getColorSchemeManager();

    const satData = catalogManagerInstance.objectCache;

    getEl('search-results')!.innerHTML = results.reduce((html, result) => {
      const obj = <DetailedSatellite | MissileObject>satData[result.id];

      // 再次确保不显示碎片，即使它们意外进入了结果列表
      if (obj.type === SpaceObjectType.DEBRIS || (obj.type as any) === 3 || (obj.type as any) === '3') {
        return html;
      }

      html += `<div class="search-result" data-obj-id="${obj.id}">`;
      html += '<div class="truncate-search">';

      // Left half of search results
      if (obj.isMissile()) {
        html += obj.name;
      } else if (result.searchType === SearchResultType.OBJECT_NAME) {
        // If the name matched - highlight it
        html += obj.name.substring(0, result.strIndex);
        html += '<span class="search-hilight">';
        html += obj.name.substring(result.strIndex, result.strIndex + result.patlen);
        html += '</span>';
        html += obj.name.substring(result.strIndex + result.patlen);
      } else if (obj.isSatellite() && result.searchType === SearchResultType.ALT_NAME) {
        const sat = obj as DetailedSatellite;

        // If the alternate name matched - highlight it
        html += sat.altName.substring(0, result.strIndex);
        html += '<span class="search-hilight">';
        html += sat.altName.substring(result.strIndex, result.strIndex + result.patlen);
        html += '</span>';
        html += sat.altName.substring(result.strIndex + result.patlen);
      } else {
        // If not, just write the name
        html += obj.name;
      }
      html += '</div>';
      html += '<div class="search-result-scc">';

      // Right half of search results
      switch (result.searchType) {
        case SearchResultType.NORAD_ID:
          {
            const sat = obj as DetailedSatellite;

            // If the object number matched
            result.strIndex = result.strIndex || 0;
            result.patlen = result.patlen || 5;

            html += sat.sccNum.substring(0, result.strIndex);
            html += '<span class="search-hilight">';
            html += sat.sccNum.substring(result.strIndex, result.strIndex + result.patlen);
            html += '</span>';
            html += sat.sccNum.substring(result.strIndex + result.patlen);
          }
          break;
        case SearchResultType.INTLDES:
          {
            const sat = obj as DetailedSatellite;
            // If the international designator matched

            result.strIndex = result.strIndex || 0;
            result.patlen = result.patlen || 5;

            html += sat.intlDes.substring(0, result.strIndex);
            html += '<span class="search-hilight">';
            html += sat.intlDes.substring(result.strIndex, result.strIndex + result.patlen);
            html += '</span>';
            html += sat.intlDes.substring(result.strIndex + result.patlen);
          }
          break;
        case SearchResultType.BUS:
          {
            const sat = obj as DetailedSatellite;
            // If the object number matched

            result.strIndex = result.strIndex || 0;
            result.patlen = result.patlen || 5;

            html += sat.bus.substring(0, result.strIndex);
            html += '<span class="search-hilight">';
            html += sat.bus.substring(result.strIndex, result.strIndex + result.patlen);
            html += '</span>';
            html += sat.bus.substring(result.strIndex + result.patlen);
          }
          break;
        case SearchResultType.LAUNCH_VEHICLE:
          {
            const sat = obj as DetailedSatellite;

            result.strIndex = result.strIndex || 0;
            result.patlen = result.patlen || 5;

            html += sat.launchVehicle.substring(0, result.strIndex);
            html += '<span class="search-hilight">';
            html += sat.launchVehicle.substring(result.strIndex, result.strIndex + result.patlen);
            html += '</span>';
            html += sat.launchVehicle.substring(result.strIndex + result.patlen);
          }
          break;
        case SearchResultType.MISSILE:
          {
            const misl = obj as MissileObject;

            html += misl.desc;
          }
          break;
        case SearchResultType.STAR:
          html += 'Star';
          break;
        default:
          if (obj.isMissile()) {
            const misl = obj as MissileObject;

            html += misl.desc;
          } else if (obj.isStar()) {
            html += 'Star';
          } else if (obj.isSatellite()) {
            const sat = obj as DetailedSatellite;

            html += sat.sccNum;
          }
          break;
      }

      html += '</div></div>';

      return html;
    }, '');

    const satInfoboxDom = getEl('sat-infobox', true);

    if (satInfoboxDom) {
      SatInfoBox.resetMenuLocation(satInfoboxDom, false);
    }

    if (!settingsManager.isEmbedMode) {
      // 🔥🔥🔥 动态调整搜索结果框高度 🔥🔥🔥
      this.adjustSearchResultsHeight_();

      slideInDown(getEl('search-results')!, 1000);

      // 🔥🔥🔥 在动画完成后再次调整高度，确保不被动画覆盖
      setTimeout(() => {
        this.adjustSearchResultsHeight_();
      }, 1100);

      this.isResultsOpen = true;
    }

    colorSchemeManagerInstance.isUseGroupColorScheme = true;

    // 强制重新计算颜色缓冲区以确保搜索结果正确显示
    setTimeout(() => {
      colorSchemeManagerInstance.calculateColorBuffers(true);
    }, 100);
  }

  toggleSearch() {
    if (!this.isSearchOpen) {
      this.openSearch();
    } else {
      this.closeSearch();
    }
  }

  closeSearch(isForce = false) {
    if (!this.isSearchOpen && !isForce) {
      return;
    }

    this.isSearchOpen = false;
    getEl('search-holder')?.classList.remove('search-slide-down');
    getEl('search-holder')?.classList.add('search-slide-up');
    this.uiManager_.hideSideMenus();
    this.hideResults();

    // 确保完全清除搜索状态
    const searchInput = getEl('search') as HTMLInputElement;
    if (searchInput) {
      searchInput.value = '';
    }
    settingsManager.lastSearch = '';
    settingsManager.lastSearchResults = [];
  }

  /**
   * 🔥🔥🔥 动态调整搜索结果框高度，根据搜索结果数量自动调整
   */
  private adjustSearchResultsHeight_(): void {
    const searchResultsEl = getEl('search-results');
    if (!searchResultsEl) return;

    // 获取搜索结果项的数量
    const resultItems = searchResultsEl.querySelectorAll('.search-result');
    const resultCount = resultItems.length;

    if (resultCount === 0) {
      // 没有结果时隐藏
      searchResultsEl.style.display = 'none';
      return;
    }

    // 计算每个搜索结果项的高度（包括padding）
    const itemHeight = 40; // 大约每个搜索结果项的高度
    const padding = 20; // 顶部和底部的padding
    const border = 5; // 底部边框

    // 计算理想高度
    const idealHeight = (resultCount * itemHeight) + padding + border;

    // 获取屏幕可用高度
    const topMenuHeight = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--top-menu-height') || '60');
    const bottomMenuHeight = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--bottom-menu-height') || '120');
    const maxHeight = window.innerHeight - topMenuHeight - bottomMenuHeight;

    // 设置高度：使用理想高度，但不超过最大高度
    const finalHeight = Math.min(idealHeight, maxHeight);

    // 只设置高度和滚动相关的样式，不影响背景等其他样式
    searchResultsEl.style.height = `${finalHeight}px`;
    searchResultsEl.style.maxHeight = `${maxHeight}px`;

    // 如果内容超过容器高度，启用滚动
    if (idealHeight > maxHeight) {
      searchResultsEl.style.overflowY = 'auto';
    } else {
      searchResultsEl.style.overflowY = 'hidden';
    }
  }

  openSearch(isForce = false) {
    if (this.isSearchOpen && !isForce) {
      return;
    }

    this.isSearchOpen = true;
    getEl('search-holder')?.classList.remove('search-slide-up');
    getEl('search-holder')?.classList.add('search-slide-down');

    const searchDom = <HTMLInputElement>getEl('search');

    if (searchDom) {
      const curSearch = searchDom.value;

      if (curSearch.length > settingsManager.minimumSearchCharacters) {
        this.doSearch(curSearch);
      }
    }
  }
}
