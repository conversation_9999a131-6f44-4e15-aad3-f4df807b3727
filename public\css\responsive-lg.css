@media (min-width: 1024px) and (max-width: 1279px) {
  :root {
    --nav-bar-height: 35px;
  }
}

@media (min-width: 1024px) {
  #sensor-selected-container {
    width: 100%;
    padding: 0px 10px;
  }

  #fullscreen-icon {
    display: none;
  }

  #menu-launches,
  #menu-record,
  #menu-color-scheme,
  #social,
  #fastCompSettings {
    display: block;
  }

  .top-menu-icons>a {
    padding: 0px 10px;
  }

  .search-icon-search-on {
    transition: 1s;
  }

  .top-menu-icons img {
    width: 25px;
    height: 25px;
  }

  #search-close {
    padding: 0px 6.25%;
    font-size: 24px;
  }

  /* TODO: Account for the bottom bar being minimized */
  #search-results {
    overflow-x: hidden;
    overflow-y: auto;
    display: none;
    position: absolute;
    right: 0px;
    width: 355px;
    /* 🔥 半透明模糊背景效果 */
    background: rgba(0, 0, 0, 0.7) !important; /* 半透明黑色背景 */
    backdrop-filter: blur(15px) !important; /* 背景模糊效果 */
    -webkit-backdrop-filter: blur(15px) !important;
    z-index: 1;
    top: var(--top-menu-height);
    /* 🔥🔥🔥 移除固定bottom，让高度自动调整 */
    /* bottom: var(--bottom-menu-top); */
    height: auto !important; /* 🔥🔥🔥 强制自动高度 */
    max-height: calc(100vh - var(--top-menu-height) - var(--bottom-menu-height)) !important;
    min-height: auto !important; /* 🔥🔥🔥 自动最小高度 */
    /* 🔥 移除边框，使用半透明边框 */
    border: 1px solid rgba(255, 255, 255, 0.2) !important; /* 半透明白色边框 */
    border-radius: 8px !important; /* 添加圆角 */
    padding-top: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important; /* 阴影效果 */
  }

  .share-icons {
    position: absolute;
    left: 0px;
    z-index: 1;
    width: 50px;
    height: 50px;
    padding: 9px;
    background: var(--color-dark-background) !important;
  }

  .share-up {
    transition: 1s;
    top: 0px !important;
  }

  #time-machine-menu {
    padding-left: 35px;
    color: white;
    background: var(--color-dark-background);
    top: 50px;
    right: 0px;
    z-index: 100;
    position: absolute;
    overflow: auto;
    width: 100%;
    border-width: 0px 0px 5px 0px;
    border-style: solid;
    border-color: var(--color-dark-border);
  }

  #obfit-menu {
    width: 500px;
  }

  /* ::-webkit-scrollbar {
    display: none;
  } */

  .search-slide-down {
    width: 355px;
    /* This should match the #search-results */
  }

  .search-slide-up {
    width: 0px;
  }

  #legend-hover-menu {
    top: var(--top-menu-height);
    padding: 5px;
  }

  .Square-Box {
    width: calc(25px / var(--system-scale-factor, 1)) !important;
    height: calc(25px / var(--system-scale-factor, 1)) !important;
    /* 保留原有的颜色和样式属性 */
    border-width: calc(2px / var(--system-scale-factor, 1)) !important;
    border-style: solid !important;
    border-radius: calc(12px / var(--system-scale-factor, 1)) !important;
    margin-right: calc(15px / var(--system-scale-factor, 1)) !important;
    cursor: pointer !important;
    box-shadow: 0 0px calc(4px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.2),
                0 0px calc(6px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.19) !important;
  }

  #sat-infobox {
    /* 🔥 移除强制位置设置，让Draggabilly控制位置 */
    margin-top: 25px;
    width: 355px;
    max-height: 60%;
  }

  .satinfo-fixed:after {
    content: '';
    height: calc(var(--bottom-menu-height) + 15px);
    display: block;
  }

  .sat-info-value {
    float: right;
    width: 220px;
    padding: 0px 25px;
    text-align: center;
  }

  .truncate-search {
    width: 200px;
  }
}