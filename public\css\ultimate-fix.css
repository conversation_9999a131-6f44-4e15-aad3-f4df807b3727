/*
 * 🔥🔥🔥 终极修复CSS - 最高优先级覆盖所有问题
 * 创建时间: 2025-01-28
 * 目标: 系统性解决所有菜单样式问题
 */

/* ========================================
 * 0. 设置菜单和卫星视场输入框修复 - 基于调试结果的精确修复
 * ======================================== */

/* 🔥🔥🔥 精确修复：基于调试发现的问题，使用最高优先级覆盖错误的CSS规则 🔥🔥🔥 */
html body form#settings-form input#maxSearchSats,
html body form#settings-form input#satFieldOfView,
html body div#settings-menu input#maxSearchSats,
html body div#settings-menu input#satFieldOfView,
html body #settings-menu input#maxSearchSats,
html body #settings-menu input#satFieldOfView,
html body input#maxSearchSats,
html body input#satFieldOfView,
body input#maxSearchSats,
body input#satFieldOfView,
input#maxSearchSats,
input#satFieldOfView,
#maxSearchSats,
#satFieldOfView {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.8) !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  outline: none !important;
}

/* 聚焦状态 */
html body form#settings-form input#maxSearchSats:focus,
html body form#settings-form input#satFieldOfView:focus,
html body input#maxSearchSats:focus,
html body input#satFieldOfView:focus,
input#maxSearchSats:focus,
input#satFieldOfView:focus,
#maxSearchSats:focus,
#satFieldOfView:focus {
  border-bottom: 1px solid rgba(255, 255, 255, 1.0) !important;
}

/* ========================================
 * 0. 输入框样式修复 - 最简单直接的方式
 * ======================================== */

/* 🔥🔥🔥 超强优先级：使用最强的CSS选择器 🔥🔥🔥 */
/* 使用多重选择器和伪类来确保最高优先级 */
html body div.input-field input[type='text']:not(.browser-default)#maxSearchSats,
html body div.input-field input[type='text']:not(.browser-default)#satFieldOfView,
html body div.input-field input[type='text']:not(.browser-default)#sat-fov-default-fov-angle,
html body div.input-field input[type='text']:not(.browser-default)#sat-fov-fov-angle,
html body div.input-field input[type='text']:not(.browser-default)#sat-fov-range,
html body div input[type='text']:not(.browser-default)#maxSearchSats,
html body div input[type='text']:not(.browser-default)#satFieldOfView,
html body input[type='text']:not(.browser-default)#maxSearchSats,
html body input[type='text']:not(.browser-default)#satFieldOfView,
body div.input-field input[type='text']#maxSearchSats,
body div.input-field input[type='text']#satFieldOfView,
div.input-field input[type='text']#maxSearchSats,
div.input-field input[type='text']#satFieldOfView,
input[type='text']#maxSearchSats,
input[type='text']#satFieldOfView {
  border: none !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
  border-radius: 0 !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 聚焦时的样式 */
html body div.input-field input[type='text']:not(.browser-default)#maxSearchSats:focus:not([readonly]),
html body div.input-field input[type='text']:not(.browser-default)#satFieldOfView:focus:not([readonly]),
html body div.input-field input[type='text']:not(.browser-default)#sat-fov-default-fov-angle:focus:not([readonly]),
html body div.input-field input[type='text']:not(.browser-default)#sat-fov-fov-angle:focus:not([readonly]),
html body div.input-field input[type='text']:not(.browser-default)#sat-fov-range:focus:not([readonly]),
html body input[type='text']#maxSearchSats:focus,
html body input[type='text']#satFieldOfView:focus,
input[type='text']#maxSearchSats:focus,
input[type='text']#satFieldOfView:focus {
  border-bottom: 1px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
}



/* ========================================
 * 1. 菜单标题修复 - 最高优先级
 * ======================================== */

/* 🔥🔥🔥 强制所有侧边栏菜单标题居中且足够大 */
html[lang] body .side-menu h4,
html[lang] body .side-menu h5,
html[lang] body .side-menu .menu-title,
html[lang] body [id$="-menu"] h4,
html[lang] body [id$="-menu"] h5,
html[lang] body [id$="-menu"] .menu-title,
html[lang] body #settings-menu h4,
html[lang] body #settings-menu h5,
html[lang] body #sensor-timeline-menu h4,
html[lang] body #sensor-timeline-menu h5,
html[lang] body #satellite-timeline-menu h4,
html[lang] body #satellite-timeline-menu h5 {
  font-size: calc(32px / var(--system-scale-factor, 1)) !important;
  line-height: calc(38px / var(--system-scale-factor, 1)) !important;
  margin: calc(20px / var(--system-scale-factor, 1)) 0 calc(15px / var(--system-scale-factor, 1)) 0 !important;
  color: white !important;
  font-weight: bold !important;
  text-align: center !important;
  width: 100% !important;
  display: block !important;
}

/* ========================================
 * 2. 开关按钮背景框移除 - 最高优先级
 * ======================================== */

/* 🔥🔥🔥 强制移除所有开关按钮的背景框 */
html[lang] body .side-menu .switch,
html[lang] body [id$="-menu"] .switch,
html[lang] body #settings-menu .switch,
html[lang] body #sensor-timeline-menu .switch,
html[lang] body #satellite-timeline-menu .switch {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: calc(10px / var(--system-scale-factor, 1)) 0 !important;
}

/* 🔥🔥🔥 强制移除开关标签的背景框 */
html[lang] body .side-menu .switch label,
html[lang] body [id$="-menu"] .switch label,
html[lang] body #settings-menu .switch label,
html[lang] body #sensor-timeline-menu .switch label,
html[lang] body #satellite-timeline-menu .switch label {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
  color: white !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

/* ========================================
 * 3. 侧边栏按钮文字居中 - 最高优先级
 * ======================================== */

/* 🔥🔥🔥 强制所有侧边栏功能按钮文字居中 */
html[lang] body .side-menu .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
html[lang] body .side-menu .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
html[lang] body .side-menu .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
html[lang] body .side-menu button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
html[lang] body [id$="-menu"] .btn:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
html[lang] body [id$="-menu"] .btn-large:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
html[lang] body [id$="-menu"] .btn-small:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)),
html[lang] body [id$="-menu"] button:not(.icon-btn):not(.param-btn):not(.satellite-btn):not([id*="settings-color-"]):not([id$="-settings-btn"]):not([id$="-close-btn"]):not([id$="-download-btn"]):not([id$="-list-btn"]):not(:has(.material-icons)) {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  line-height: 1.2 !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding-left: calc(16px / var(--system-scale-factor, 1)) !important;
  padding-right: calc(16px / var(--system-scale-factor, 1)) !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  text-indent: 0 !important;
  letter-spacing: normal !important;
  box-sizing: border-box !important;
}

/* 🔥🔥🔥 移除可能影响居中的伪元素 */
html[lang] body .side-menu .btn:before,
html[lang] body .side-menu .btn:after,
html[lang] body .side-menu .btn-large:before,
html[lang] body .side-menu .btn-large:after,
html[lang] body .side-menu .btn-small:before,
html[lang] body .side-menu .btn-small:after,
html[lang] body .side-menu button:before,
html[lang] body .side-menu button:after,
html[lang] body [id$="-menu"] .btn:before,
html[lang] body [id$="-menu"] .btn:after,
html[lang] body [id$="-menu"] .btn-large:before,
html[lang] body [id$="-menu"] .btn-large:after,
html[lang] body [id$="-menu"] .btn-small:before,
html[lang] body [id$="-menu"] .btn-small:after,
html[lang] body [id$="-menu"] button:before,
html[lang] body [id$="-menu"] button:after {
  display: none !important;
  content: none !important;
  width: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* ========================================
 * 4. 颜色图例尺寸修复 - 最高优先级
 * ======================================== */

/* 🔥🔥🔥 强制颜色图例按钮尺寸 */
html[lang] body #settings-menu #settings-colors input[type="color"],
html[lang] body #settings-colors input[type="color"],
html[lang] body div#settings-menu div#settings-colors input[type="color"],
html[lang] body #settings-color-payload,
html[lang] body #settings-color-rocketBody,
html[lang] body #settings-color-debris,
html[lang] body #settings-color-inview,
html[lang] body #settings-color-missile,
html[lang] body #settings-color-missileInview,
html[lang] body #settings-color-special {
  width: calc(18px / var(--system-scale-factor, 1)) !important;
  height: calc(10px / var(--system-scale-factor, 1)) !important;
  min-width: calc(18px / var(--system-scale-factor, 1)) !important;
  min-height: calc(10px / var(--system-scale-factor, 1)) !important;
  max-width: calc(18px / var(--system-scale-factor, 1)) !important;
  max-height: calc(10px / var(--system-scale-factor, 1)) !important;
  border-radius: calc(5px / var(--system-scale-factor, 1)) !important;
  margin: calc(1px / var(--system-scale-factor, 1)) auto !important;
  padding: 0 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  display: block !important;
}

/* ========================================
 * 5. 设置菜单弹出层z-index修复
 * ======================================== */

/* 🔥🔥🔥 确保设置菜单正常显示 - 重置z-index */
html[lang] body #settings-menu,
html[lang] body .side-menu-parent#settings-menu,
html[lang] body div#settings-menu {
  z-index: 1000 !important;
  position: fixed !important;
  /* 移除强制显示规则，让正常的显示/隐藏逻辑工作 */
  /* display: block !important; */
  /* visibility: visible !important; */
}

/* 🔥🔥🔥 传感器时间线设置菜单为左侧覆盖显示 */
html[lang] body #sensor-timeline-menu-secondary:not(.start-hidden) {
  z-index: 100000 !important;
  position: fixed !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.7) !important;
  padding: 20px !important;
  margin: 0 !important;
  top: 0 !important;
  left: 0 !important;
  width: 400px !important;
  height: 100vh !important;
  max-width: 400px !important;
  max-height: 100vh !important;
  overflow-y: auto !important;
  display: block !important;
  visibility: visible !important;
  transform: translateX(0) !important;
}

/* 🔥🔥🔥 卫星时间线设置菜单为左侧覆盖显示（与传感器时间线相同） */
html[lang] body #satellite-timeline-menu-secondary:not(.start-hidden) {
  z-index: 100000 !important;
  position: fixed !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.7) !important;
  padding: 20px !important;
  margin: 0 !important;
  top: 0 !important;
  left: 0 !important;
  width: 400px !important;
  height: 100vh !important;
  max-width: 400px !important;
  max-height: 100vh !important;
  overflow-y: auto !important;
  display: block !important;
  visibility: visible !important;
  transform: translateX(0) !important;
}

/* 🔥 普通侧边菜单设置保持侧面显示 */
html[lang] body .side-menu-secondary:not(.start-hidden):not([id$="-timeline-menu-secondary"]) {
  z-index: 1002 !important;
  position: absolute !important;
  background: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.7) !important;
  padding: 20px !important;
  margin: 0 !important;
  top: var(--top-menu-height) !important;
  bottom: var(--bottom-menu-top) !important;
  left: auto !important;
  right: auto !important;
  transform: translateX(-100%) !important;
  max-width: 400px !important;
  max-height: none !important;
  height: auto !important;
  overflow-y: auto !important;
  display: block !important;
  visibility: visible !important;
  transition: all 1s ease-in-out !important;
}

/* 🔥 确保隐藏状态的设置菜单真正隐藏 */
html[lang] body #sensor-timeline-menu-secondary.start-hidden,
html[lang] body #satellite-timeline-menu-secondary.start-hidden,
html[lang] body [id$="-timeline-menu-secondary"].start-hidden,
html[lang] body .side-menu-secondary.start-hidden,
html[lang] body .side-menu-parent.start-hidden .side-menu-secondary {
  display: none !important;
  visibility: hidden !important;
}

/* ========================================
 * 6. 输入框透明度终极修复 - 覆盖所有可能的样式冲突
 * ======================================== */

/* 🔥🔥🔥 强制所有历史菜单和watchlist输入框透明 - 最高优先级 */
html[lang] body #geo-longitude-history-menu input,
html[lang] body #history-track-menu input,
html[lang] body #watchlist-menu input,
html[lang] body #geo-longitude-norad-id,
html[lang] body #geo-longitude-start-date,
html[lang] body #geo-longitude-end-date,
html[lang] body #geo-longitude-range,
html[lang] body #history-norad-id,
html[lang] body #history-start-date,
html[lang] body #history-end-date,
html[lang] body #watchlist-new,
#geo-longitude-history-menu input,
#history-track-menu input,
#watchlist-menu input,
#geo-longitude-norad-id,
#geo-longitude-start-date,
#geo-longitude-end-date,
#geo-longitude-range,
#history-norad-id,
#history-start-date,
#history-end-date,
#watchlist-new {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
  border-radius: 0 !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 🔥🔥🔥 强制输入框聚焦状态透明 - 覆盖浏览器默认样式 */
html[lang] body #geo-longitude-history-menu input:focus,
html[lang] body #history-track-menu input:focus,
html[lang] body #watchlist-menu input:focus,
html[lang] body #geo-longitude-norad-id:focus,
html[lang] body #geo-longitude-start-date:focus,
html[lang] body #geo-longitude-end-date:focus,
html[lang] body #geo-longitude-range:focus,
html[lang] body #history-norad-id:focus,
html[lang] body #history-start-date:focus,
html[lang] body #history-end-date:focus,
html[lang] body #watchlist-new:focus,
#geo-longitude-history-menu input:focus,
#history-track-menu input:focus,
#watchlist-menu input:focus,
#geo-longitude-norad-id:focus,
#geo-longitude-start-date:focus,
#geo-longitude-end-date:focus,
#geo-longitude-range:focus,
#history-norad-id:focus,
#history-start-date:focus,
#history-end-date:focus,
#watchlist-new:focus {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 0 !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* 🔥🔥🔥 强制输入框自动填充状态透明 - 覆盖浏览器自动填充样式 */
html[lang] body #geo-longitude-history-menu input:-webkit-autofill,
html[lang] body #history-track-menu input:-webkit-autofill,
html[lang] body #watchlist-menu input:-webkit-autofill,
html[lang] body #geo-longitude-norad-id:-webkit-autofill,
html[lang] body #geo-longitude-start-date:-webkit-autofill,
html[lang] body #geo-longitude-end-date:-webkit-autofill,
html[lang] body #geo-longitude-range:-webkit-autofill,
html[lang] body #history-norad-id:-webkit-autofill,
html[lang] body #history-start-date:-webkit-autofill,
html[lang] body #history-end-date:-webkit-autofill,
html[lang] body #watchlist-new:-webkit-autofill,
#geo-longitude-history-menu input:-webkit-autofill,
#history-track-menu input:-webkit-autofill,
#watchlist-menu input:-webkit-autofill,
#geo-longitude-norad-id:-webkit-autofill,
#geo-longitude-start-date:-webkit-autofill,
#geo-longitude-end-date:-webkit-autofill,
#geo-longitude-range:-webkit-autofill,
#history-norad-id:-webkit-autofill,
#history-start-date:-webkit-autofill,
#history-end-date:-webkit-autofill,
#watchlist-new:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
  -webkit-text-fill-color: white !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
  border-radius: 0 !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* ========================================
 * 6. 强制覆盖所有可能的冲突样式
 * ======================================== */

/* 🔥🔥🔥 移除所有可能影响的透明样式 */
html[lang] body .side-menu .switch,
html[lang] body [id$="-menu"] .switch {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* 🔥🔥🔥 确保开关滑动条正常显示 */
html[lang] body .side-menu .switch label .lever,
html[lang] body [id$="-menu"] .switch label .lever {
  background: rgba(255, 255, 255, 0.3) !important;
  background-color: rgba(255, 255, 255, 0.3) !important;
}

html[lang] body .side-menu .switch label input[type=checkbox]:checked + .lever,
html[lang] body [id$="-menu"] .switch label input[type=checkbox]:checked + .lever {
  background: #2196f3 !important;
  background-color: #2196f3 !important;
}

/* ========================================
 * 7. 卫星监视列表间距修复 - 超高优先级
 * ======================================== */

/* 🔥🔥🔥 强制卫星监视列表紧凑布局 - 只针对列表内的行，不影响按钮行 */
html[lang] body div#watchlist-menu div#watchlist-content div#watchlist-list div.row,
html[lang] body #watchlist-menu #watchlist-content #watchlist-list .row,
html[lang] body #watchlist-content #watchlist-list .row,
html[lang] body #watchlist-list .row,
.side-menu #watchlist-list .row {
  margin-bottom: calc(1px / var(--system-scale-factor, 1)) !important;
  margin-top: calc(1px / var(--system-scale-factor, 1)) !important;
  padding: calc(2px / var(--system-scale-factor, 1)) 0 !important;
  min-height: auto !important;
  height: auto !important;
}

/* 🔥🔥🔥 强制卫星监视列表列间距超紧凑 - 只针对列表内的列 */
html[lang] body div#watchlist-menu div#watchlist-content div#watchlist-list div.row div.col,
html[lang] body #watchlist-menu #watchlist-content #watchlist-list .row .col,
html[lang] body #watchlist-content #watchlist-list .col,
html[lang] body #watchlist-list .col,
.side-menu #watchlist-list .col,
#watchlist-list .col.s3,
#watchlist-list .col.s7,
#watchlist-list .col.s2,
#watchlist-list .col.m3,
#watchlist-list .col.m7,
#watchlist-list .col.m2,
#watchlist-list .col.l3,
#watchlist-list .col.l7,
#watchlist-list .col.l2 {
  padding: 0 calc(1px / var(--system-scale-factor, 1)) !important;
  margin: 0 !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
  height: auto !important;
  min-height: auto !important;
}

/* 🔥🔥🔥 强制卫星监视列表文字和图标紧凑 - 只调整间距不改变文字大小 */
html[lang] body #watchlist-menu .sat-sccnum,
html[lang] body #watchlist-menu .sat-name,
html[lang] body #watchlist-content .sat-sccnum,
html[lang] body #watchlist-content .sat-name {
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
  padding: calc(2px / var(--system-scale-factor, 1)) 0 !important;
  margin: 0 !important;
  display: block !important;
}

/* 🔥🔥🔥 强制卫星监视列表移除图标紧凑 */
html[lang] body #watchlist-menu .watchlist-remove,
html[lang] body #watchlist-content .watchlist-remove,
html[lang] body #watchlist-menu .remove-icon img,
html[lang] body #watchlist-content .remove-icon img {
  width: calc(16px / var(--system-scale-factor, 1)) !important;
  height: calc(16px / var(--system-scale-factor, 1)) !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 🔥🔥🔥 强制卫星监视列表整体容器紧凑 */
html[lang] body #watchlist-menu #watchlist-list,
html[lang] body #watchlist-content #watchlist-list {
  padding: 0 !important;
  margin: 0 !important;
  margin-bottom: calc(16px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 强制卫星监视列表标题间距 - 与其他菜单一致 */
html[lang] body #watchlist-menu h5,
html[lang] body #watchlist-content h5 {
  margin-top: calc(8px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(8px / var(--system-scale-factor, 1)) !important;
  padding: 0 !important;
}

/* 🔥🔥🔥 强制卫星监视列表输入框区域 - 与其他菜单一致 */
html[lang] body #watchlist-menu .input-field,
html[lang] body #watchlist-content .input-field {
  margin-top: calc(8px / var(--system-scale-factor, 1)) !important;
  margin-bottom: calc(8px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 新增卫星输入框和按钮的flex布局优化 */
html[lang] body #watchlist-menu .row:has(#watchlist-new),
html[lang] body #watchlist-content .row:has(#watchlist-new) {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 0 !important;
  gap: calc(10px / var(--system-scale-factor, 1)) !important;
}

/* 🔥🔥🔥 新增卫星输入框容器flex设置 */
html[lang] body #watchlist-menu .input-field:has(#watchlist-new),
html[lang] body #watchlist-content .input-field:has(#watchlist-new) {
  flex: 1 !important;
  margin-right: 0 !important;
  margin-bottom: 0 !important;
}

/* 🔥🔥🔥 新增按钮图标优化 */
html[lang] body #watchlist-add {
  width: calc(24px / var(--system-scale-factor, 1)) !important;
  height: calc(24px / var(--system-scale-factor, 1)) !important;
  vertical-align: middle !important;
  flex-shrink: 0 !important;
  padding-top: 0 !important; /* 覆盖原来的padding-top: 35px */
  margin: 0 !important;
  filter: hue-rotate(210deg) brightness(1.2) !important;
}

/* 🔥🔥🔥 覆盖原来的.add-icon样式，避免影响新布局 */
html[lang] body #watchlist-menu .add-icon img,
html[lang] body #watchlist-content .add-icon img {
  padding-top: 0 !important;
  margin: 0 !important;
}

/* 🔥🔥🔥 超级强制修复watchlist输入框透明度 - 所有状态 */
html[lang] body #watchlist-new,
html[lang] body #watchlist-new:focus,
html[lang] body #watchlist-new:active,
html[lang] body #watchlist-new:hover,
html[lang] body #watchlist-new:-webkit-autofill,
html[lang] body #watchlist-new:-webkit-autofill:focus,
html[lang] body #watchlist-new:-webkit-autofill:hover,
html[lang] body #watchlist-new:-webkit-autofill:active,
#watchlist-new,
#watchlist-new:focus,
#watchlist-new:active,
#watchlist-new:hover,
#watchlist-new:-webkit-autofill,
#watchlist-new:-webkit-autofill:focus,
#watchlist-new:-webkit-autofill:hover,
#watchlist-new:-webkit-autofill:active {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
  border-radius: 0 !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
  -webkit-text-fill-color: white !important;
  transition: border-bottom-color 0.2s ease !important;
}

/* watchlist输入框聚焦时下划线更亮 */
html[lang] body #watchlist-new:focus,
#watchlist-new:focus {
  border-bottom-color: rgba(255, 255, 255, 0.8) !important;
}









/* ========================================
 * 8. 开关按钮边框移除 - 最高优先级
 * ======================================== */

/* 🔥🔥🔥 强制移除所有开关按钮的边框 - 最高优先级 */
html[lang] body .switch,
html[lang] body div .switch,
html[lang] body .side-menu .switch,
html[lang] body [id$="-menu"] .switch,
html[lang] body div .side-menu .switch,
html[lang] body div [id$="-menu"] .switch {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
}

/* 🔥🔥🔥 强制移除开关按钮标签的边框 - 最高优先级 */
html[lang] body .switch label,
html[lang] body div .switch label,
html[lang] body .side-menu .switch label,
html[lang] body [id$="-menu"] .switch label,
html[lang] body div .side-menu .switch label,
html[lang] body div [id$="-menu"] .switch label {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
}

/* 🔥🔥🔥 强制移除开关按钮滑块的边框 - 最高优先级 */
html[lang] body .switch label .lever,
html[lang] body div .switch label .lever,
html[lang] body .side-menu .switch label .lever,
html[lang] body [id$="-menu"] .switch label .lever,
html[lang] body div .side-menu .switch label .lever,
html[lang] body div [id$="-menu"] .switch label .lever {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 🔥🔥🔥 强制移除开关按钮滑块圆点的边框 - 最高优先级 */
html[lang] body .switch label .lever:after,
html[lang] body div .switch label .lever:after,
html[lang] body .side-menu .switch label .lever:after,
html[lang] body [id$="-menu"] .switch label .lever:after,
html[lang] body div .side-menu .switch label .lever:after,
html[lang] body div [id$="-menu"] .switch label .lever:after {
  border: none !important;
  outline: none !important;
}

/* 🔥🔥🔥 强制隐藏开关按钮的:before伪元素外框 - 最高优先级 🔥🔥🔥 */
html[lang] body .switch label .lever:before,
html[lang] body div .switch label .lever:before,
html[lang] body .side-menu .switch label .lever:before,
html[lang] body [id$="-menu"] .switch label .lever:before,
html[lang] body div .side-menu .switch label .lever:before,
html[lang] body div [id$="-menu"] .switch label .lever:before {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
}

/* 🔥🔥🔥 终极覆盖：强制移除设置菜单所有可能的边框 🔥🔥🔥 */
html[lang] body div#settings-menu .switch,
html[lang] body div#settings-menu .switch *,
html[lang] body div#settings-menu .switch *:before,
html[lang] body div#settings-menu .switch *:after,
html[lang] body #settings-menu .switch,
html[lang] body #settings-menu .switch *,
html[lang] body #settings-menu .switch *:before,
html[lang] body #settings-menu .switch *:after {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 🔥🔥🔥 特别针对设置菜单开关的:before伪元素完全移除 🔥🔥🔥 */
html[lang] body div#settings-menu .switch label .lever:before,
html[lang] body #settings-menu .switch label .lever:before {
  content: none !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  width: 0 !important;
  height: 0 !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
}

/* 🔥🔥🔥 强制移除开关按钮的focus状态边框 - 最高优先级 🔥🔥🔥 */
html[lang] body .switch input[type="checkbox"]:focus,
html[lang] body .switch input[type="checkbox"]:focus + .lever,
html[lang] body .switch input[type="checkbox"]:focus + .lever:before,
html[lang] body .switch input[type="checkbox"]:focus + .lever:after,
html[lang] body .switch label:focus,
html[lang] body .switch label:focus .lever,
html[lang] body .switch label:focus .lever:before,
html[lang] body .switch label:focus .lever:after,
html[lang] body div .switch input[type="checkbox"]:focus,
html[lang] body div .switch input[type="checkbox"]:focus + .lever,
html[lang] body div .switch input[type="checkbox"]:focus + .lever:before,
html[lang] body div .switch input[type="checkbox"]:focus + .lever:after,
html[lang] body div .switch label:focus,
html[lang] body div .switch label:focus .lever,
html[lang] body div .switch label:focus .lever:before,
html[lang] body div .switch label:focus .lever:after {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 🔥🔥🔥 终极解决方案：强制移除设置菜单开关的所有可能边框 - 核弹级优先级 🔥🔥🔥 */
#settings-menu .switch,
#settings-menu .switch *,
#settings-menu .switch *:before,
#settings-menu .switch *:after,
#settings-menu .switch *:hover,
#settings-menu .switch *:focus,
#settings-menu .switch *:active,
#settings-menu .switch *:visited {
  border: 0px solid transparent !important;
  border-width: 0px !important;
  border-style: none !important;
  border-color: transparent !important;
  outline: 0px solid transparent !important;
  outline-width: 0px !important;
  outline-style: none !important;
  outline-color: transparent !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
}

/* 🔥🔥🔥 根本原因修复：强制移除.switch.row容器的边框 - 宇宙级优先级 🔥🔥🔥 */
html[lang] body div#settings-menu .switch.row,
html[lang] body #settings-menu .switch.row,
html body div#settings-menu .switch.row,
html body #settings-menu .switch.row,
body div#settings-menu .switch.row,
body #settings-menu .switch.row,
div#settings-menu .switch.row,
#settings-menu .switch.row,
.switch.row {
  border: 0px solid transparent !important;
  border-width: 0px !important;
  border-style: none !important;
  border-color: transparent !important;
  border-top: 0px none transparent !important;
  border-right: 0px none transparent !important;
  border-bottom: 0px none transparent !important;
  border-left: 0px none transparent !important;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
  outline: 0px solid transparent !important;
  outline-width: 0px !important;
  outline-style: none !important;
  outline-color: transparent !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* 🔥🔥🔥 终极边框颜色覆盖：强制设置所有开关的边框颜色为透明 🔥🔥🔥 */
html[lang] body div#settings-menu .switch,
html[lang] body #settings-menu .switch,
html body div#settings-menu .switch,
html body #settings-menu .switch,
body div#settings-menu .switch,
body #settings-menu .switch,
div#settings-menu .switch,
#settings-menu .switch,
.switch {
  border-color: transparent !important;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
}

/* 🔥 历史经度页面输入框样式已移至 menu-styles-optimized.css 统一管理 */



/* 🔥🔥🔥 终极解决方案：强制移除设置菜单开关的所有可见边框 - 核弹级优先级 🔥🔥🔥 */
#settings-menu .switch.row,
#settings-menu .switch.row *:not(.lever):not(.lever:after),
#settings-menu .switch.row *:before,
#settings-menu .switch,
#settings-menu .switch *:not(.lever):not(.lever:after),
#settings-menu .switch *:before {
  border: 0px none transparent !important;
  border-width: 0px !important;
  border-style: none !important;
  border-color: transparent !important;
  border-top: 0px none transparent !important;
  border-right: 0px none transparent !important;
  border-bottom: 0px none transparent !important;
  border-left: 0px none transparent !important;
  border-top-color: transparent !important;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-left-color: transparent !important;
  border-top-width: 0px !important;
  border-right-width: 0px !important;
  border-bottom-width: 0px !important;
  border-left-width: 0px !important;
  border-top-style: none !important;
  border-right-style: none !important;
  border-bottom-style: none !important;
  border-left-style: none !important;
  outline: 0px none transparent !important;
  outline-width: 0px !important;
  outline-style: none !important;
  outline-color: transparent !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* ========================================
 * 9. 卫星监视列表覆盖层间距修复 - 最高优先级
 * ======================================== */

/* 🔥🔥🔥 强制卫星监视列表覆盖层（右上角下次过境时间）紧凑布局 */
html[lang] body #info-overlay .watchlist-object,
html[lang] body .info-overlay .watchlist-object,
html[lang] body #info-overlay h5.watchlist-object,
html[lang] body .info-overlay h5.watchlist-object {
  font-size: calc(14px / var(--system-scale-factor, 1)) !important;
  line-height: calc(18px / var(--system-scale-factor, 1)) !important;
  margin: calc(2px / var(--system-scale-factor, 1)) 0 !important;
  padding: calc(2px / var(--system-scale-factor, 1)) calc(4px / var(--system-scale-factor, 1)) !important;
  font-weight: normal !important;
}

/* 🔥🔥🔥 强制卫星监视列表覆盖层行间距紧凑 */
html[lang] body #info-overlay .row:has(.watchlist-object),
html[lang] body .info-overlay .row:has(.watchlist-object),
html[lang] body #info-overlay .watchlist-overlay-item,
html[lang] body .info-overlay .watchlist-overlay-item {
  margin-bottom: calc(1px / var(--system-scale-factor, 1)) !important;
  margin-top: calc(1px / var(--system-scale-factor, 1)) !important;
  padding: 0 !important;
  min-height: auto !important;
}

/* 🔥🔥🔥 终极悬停效果修复 - 最高优先级，覆盖强制透明背景 🔥🔥🔥 */
html[lang="zh-CN"] body #sat-infobox .sat-info-row:not(#sat-info-title):not(#sat-info-title *):hover,
html[lang="en"] body #sat-infobox .sat-info-row:not(#sat-info-title):not(#sat-info-title *):hover,
html[lang] body #sat-infobox .sat-info-row:not(#sat-info-title):not(#sat-info-title *):hover,
html body #sat-infobox .sat-info-row:not(#sat-info-title):not(#sat-info-title *):hover,
body #sat-infobox .sat-info-row:not(#sat-info-title):not(#sat-info-title *):hover,
#sat-infobox .sat-info-row:not(#sat-info-title):not(#sat-info-title *):hover,
.sat-info-row:not(#sat-info-title):not(#sat-info-title *):hover,
div.sat-info-row:not(#sat-info-title):not(#sat-info-title *):hover,
span.sat-info-row:not(#sat-info-title):not(#sat-info-title *):hover {
  background: rgba(33, 150, 243, 0.3) !important;
  background-color: rgba(33, 150, 243, 0.3) !important;
  background-image: none !important;
  border-radius: calc(4px / var(--system-scale-factor, 1)) !important;
  transition: background-color 0.2s ease !important;
}

/* ========================================
 * 🔥🔥🔥 历史经度输入框终极修复 - 最高优先级
 * ======================================== */

/* 强制移除历史经度页面输入框的box-shadow */
html[lang] body div#geo-longitude-history-menu input#geo-longitude-norad-id,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-start-date,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-end-date,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-range,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-norad-id:focus,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-start-date:focus,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-end-date:focus,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-range:focus,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-norad-id:-webkit-autofill,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-start-date:-webkit-autofill,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-end-date:-webkit-autofill,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-range:-webkit-autofill,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-norad-id:-webkit-autofill:hover,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-start-date:-webkit-autofill:hover,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-end-date:-webkit-autofill:hover,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-range:-webkit-autofill:hover,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-norad-id:-webkit-autofill:focus,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-start-date:-webkit-autofill:focus,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-end-date:-webkit-autofill:focus,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-range:-webkit-autofill:focus,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-norad-id:-webkit-autofill:active,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-start-date:-webkit-autofill:active,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-end-date:-webkit-autofill:active,
html[lang] body div#geo-longitude-history-menu input#geo-longitude-range:-webkit-autofill:active {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
  border-radius: 0 !important;
  color: white !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -webkit-text-fill-color: white !important;
}

/* ========================================
 * 🔥🔥🔥 sat-info-title 背景修复 - 最高优先级
 * ======================================== */

/* 🔥🔥🔥 强制设置 sat-info-title 为深蓝色不透明背景 - 最高优先级 🔥🔥🔥 */
html[lang="zh-CN"] body #sat-info-title,
html[lang="en"] body #sat-info-title,
html[lang] body #sat-info-title,
html body #sat-info-title,
body #sat-info-title,
#sat-info-title,
div#sat-info-title,
span#sat-info-title {
  background: #001a33 !important;
  background-color: #001a33 !important;
  /* 🔥 移除强制background-image: none，让子元素的国旗可以显示 */
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  /* 🔥 移除调试边框 */
}

/* 🔥🔥🔥 终极搜索框透明背景修复 - 最高特异性，覆盖所有第三方CSS 🔥🔥🔥 */
html[lang] body div#nav-wrapper div#search-holder input#search,
html[lang] body div#search-holder input#search,
html[lang] body input#search,
html body div#nav-wrapper div#search-holder input#search,
html body div#search-holder input#search,
html body input#search,
body div#nav-wrapper div#search-holder input#search,
body div#search-holder input#search,
body input#search,
div#nav-wrapper div#search-holder input#search,
div#search-holder input#search,
nav div#search-holder input#search,
#nav-wrapper #search-holder input#search,
#search-holder input#search,
input#search,
#search {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 0 !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -ms-box-shadow: none !important;
  -o-box-shadow: none !important;
}

/* 🔥🔥🔥 终极搜索框聚焦状态透明背景修复 🔥🔥🔥 */
html[lang] body div#nav-wrapper div#search-holder input#search:focus,
html[lang] body div#nav-wrapper div#search-holder input#search:active,
html[lang] body div#search-holder input#search:focus,
html[lang] body div#search-holder input#search:active,
html[lang] body input#search:focus,
html[lang] body input#search:active,
html body div#nav-wrapper div#search-holder input#search:focus,
html body div#nav-wrapper div#search-holder input#search:active,
html body div#search-holder input#search:focus,
html body div#search-holder input#search:active,
html body input#search:focus,
html body input#search:active,
body div#nav-wrapper div#search-holder input#search:focus,
body div#nav-wrapper div#search-holder input#search:active,
body div#search-holder input#search:focus,
body div#search-holder input#search:active,
body input#search:focus,
body input#search:active,
div#nav-wrapper div#search-holder input#search:focus,
div#nav-wrapper div#search-holder input#search:active,
div#search-holder input#search:focus,
div#search-holder input#search:active,
nav div#search-holder input#search:focus,
nav div#search-holder input#search:active,
#nav-wrapper #search-holder input#search:focus,
#nav-wrapper #search-holder input#search:active,
#search-holder input#search:focus,
#search-holder input#search:active,
input#search:focus,
input#search:active,
#search:focus,
#search:active {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.9) !important;
  border-radius: 0 !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -ms-box-shadow: none !important;
  -o-box-shadow: none !important;
}

/* 🔥🔥🔥 终极搜索结果框半透明背景修复 - 最高特异性，覆盖所有第三方CSS 🔥🔥🔥 */
html[lang] body div#ui-wrapper div#search-results,
html[lang] body div#search-results,
html[lang] body #search-results,
html body div#ui-wrapper div#search-results,
html body div#search-results,
html body #search-results,
body div#ui-wrapper div#search-results,
body div#search-results,
body #search-results,
div#ui-wrapper div#search-results,
div#search-results,
#ui-wrapper #search-results,
#search-results {
  background: rgba(0, 0, 0, 0.7) !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  background-image: none !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  -moz-backdrop-filter: blur(15px) !important;
  -ms-backdrop-filter: blur(15px) !important;
  -o-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  -moz-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  -ms-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  -o-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* 🔥🔥🔥 禁用 sat-info-title 的悬停效果 - 最高优先级 🔥🔥🔥 */
html[lang="zh-CN"] body #sat-info-title:hover,
html[lang="en"] body #sat-info-title:hover,
html[lang] body #sat-info-title:hover,
html body #sat-info-title:hover,
body #sat-info-title:hover,
#sat-info-title:hover,
div#sat-info-title:hover,
span#sat-info-title:hover {
  background: #001a33 !important;
  background-color: #001a33 !important;
  /* 🔥 移除强制background-image: none，让子元素的国旗可以显示 */
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  cursor: move !important;
  /* 🔥 移除调试边框 */
}

/* 🔥🔥🔥 超级简单直接的悬停效果 - 确保生效 🔥🔥🔥 */
html[lang="zh-CN"] body #sat-infobox .sat-info-row:hover,
html[lang="en"] body #sat-infobox .sat-info-row:hover,
html[lang] body #sat-infobox .sat-info-row:hover,
html body #sat-infobox .sat-info-row:hover,
body #sat-infobox .sat-info-row:hover,
div#sat-infobox .sat-info-row:hover,
#sat-infobox .sat-info-row:hover,
.sat-info-row:hover {
  background: rgba(33, 150, 243, 0.3) !important;
  background-color: rgba(33, 150, 243, 0.3) !important;
  background-image: none !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
  cursor: default !important;
  pointer-events: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
}

/* 🔥🔥🔥 章节标题悬停效果 🔥🔥🔥 */
html[lang="zh-CN"] body #sat-infobox .sat-info-section-header:hover,
html[lang="en"] body #sat-infobox .sat-info-section-header:hover,
html[lang] body #sat-infobox .sat-info-section-header:hover,
html body #sat-infobox .sat-info-section-header:hover,
body #sat-infobox .sat-info-section-header:hover,
div#sat-infobox .sat-info-section-header:hover,
#sat-infobox .sat-info-section-header:hover,
.sat-info-section-header:hover {
  background: #0d47a1 !important;
  background-color: #0d47a1 !important;
  background-image: none !important;
  cursor: pointer !important;
  pointer-events: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
}

/* ========================================
 * 🔥🔥🔥 国旗显示修复 - 最高优先级
 * ======================================== */

/* 🔥🔥🔥 确保国旗背景图片不被透明样式覆盖 - 最高优先级 🔥🔥🔥 */
html[lang] body #sat-infobox-fi.fi,
html body #sat-infobox-fi.fi,
body #sat-infobox-fi.fi,
#sat-infobox-fi.fi {
  /* 🔥 关键：不覆盖background-image，让flag-icons库的CSS生效 */
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
  width: calc(24px / var(--system-scale-factor)) !important;
  height: calc(18px / var(--system-scale-factor)) !important;
  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  position: absolute !important;
  right: calc(5px / var(--system-scale-factor)) !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 5 !important;
  background-color: transparent !important;
  /* 🔥 移除调试边框 */
}

/* 🔥 移除重复的强制恢复规则 - 让flag-icons库自然生效 */
/* 注释掉重复的规则，避免CSS冲突 */
/*
html[lang] body #sat-infobox-fi.fi.fi-us,
html[lang] body #sat-infobox-fi.fi.fi-ru,
html[lang] body #sat-infobox-fi.fi.fi-cn,
html[lang] body #sat-infobox-fi.fi.fi-jp,
html[lang] body #sat-infobox-fi.fi.fi-in,
html[lang] body #sat-infobox-fi.fi.fi-fr,
html[lang] body #sat-infobox-fi.fi.fi-de,
html[lang] body #sat-infobox-fi.fi.fi-gb,
html[lang] body #sat-infobox-fi.fi.fi-it,
html[lang] body #sat-infobox-fi.fi.fi-ca,
html[lang] body #sat-infobox-fi.fi.fi-au,
html[lang] body #sat-infobox-fi.fi.fi-br,
html[lang] body #sat-infobox-fi.fi.fi-kr,
html[lang] body #sat-infobox-fi.fi.fi-il,
html[lang] body #sat-infobox-fi.fi.fi-mx,
html[lang] body #sat-infobox-fi.fi.fi-ar,
html[lang] body #sat-infobox-fi.fi.fi-sa,
html[lang] body #sat-infobox-fi.fi.fi-ae,
html[lang] body #sat-infobox-fi.fi.fi-tr,
html[lang] body #sat-infobox-fi.fi.fi-eg,
html[lang] body #sat-infobox-fi.fi.fi-za,
html[lang] body #sat-infobox-fi.fi.fi-ng,
html[lang] body #sat-infobox-fi.fi.fi-ma,
html[lang] body #sat-infobox-fi.fi.fi-et,
html[lang] body #sat-infobox-fi.fi.fi-ke,
html[lang] body #sat-infobox-fi.fi.fi-gh,
html[lang] body #sat-infobox-fi.fi.fi-tn,
html[lang] body #sat-infobox-fi.fi.fi-dz,
html[lang] body #sat-infobox-fi.fi.fi-ao,
html[lang] body #sat-infobox-fi.fi.fi-rw,
html[lang] body #sat-infobox-fi.fi.fi-ug,
html[lang] body #sat-infobox-fi.fi.fi-tz,
html[lang] body #sat-infobox-fi.fi.fi-mz,
html[lang] body #sat-infobox-fi.fi.fi-mg,
html[lang] body #sat-infobox-fi.fi.fi-zm,
html[lang] body #sat-infobox-fi.fi.fi-zw,
html[lang] body #sat-infobox-fi.fi.fi-bw,
html[lang] body #sat-infobox-fi.fi.fi-na,
html[lang] body #sat-infobox-fi.fi.fi-sz,
html[lang] body #sat-infobox-fi.fi.fi-ls,
html[lang] body #sat-infobox-fi.fi.fi-mw,
html[lang] body #sat-infobox-fi.fi.fi-unknown,
html[lang] body #sat-infobox-fi.fi.fi-cis,
html[lang] body #sat-infobox-fi.fi.fi-nato,
html[lang] body #sat-infobox-fi.fi.fi-su,
html[lang] body #sat-infobox-fi.fi.fi-esa,
html[lang] body #sat-infobox-fi.fi.fi-iss {
  background-image: unset !important;
}
*/
