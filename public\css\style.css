:root {
  /* 系统缩放兼容性变量 - 由JavaScript动态设置 */
  --system-scale-factor: 1;

  /* 字体大小变量 - 根据系统缩放动态调整 */
  --font-size-xs: calc(10px / var(--system-scale-factor));
  --font-size-sm: calc(12px / var(--system-scale-factor));
  --font-size-base: calc(14px / var(--system-scale-factor));
  --font-size-md: calc(16px / var(--system-scale-factor));
  --font-size-lg: calc(18px / var(--system-scale-factor));
  --font-size-xl: calc(20px / var(--system-scale-factor));
  --font-size-xxl: calc(28px / var(--system-scale-factor));

  --bottom-menu-top: 0px;
  --bottom-icon-height: calc(75px / var(--system-scale-factor));
  --bottom-icon-width: calc(115px / var(--system-scale-factor));
  --bottom-icon-img-size: calc(40px / var(--system-scale-factor));
  --bottom-filter-width: calc(185px / var(--system-scale-factor));
  --bottom-menu-height: calc(120px / var(--system-scale-factor));
  --search-box-bottom: 0px;
  --nav-bar-height: calc(35px / var(--system-scale-factor));
  /** The combination of title bar and classification bar */
  --top-menu-height: calc(var(--nav-bar-height) + var(--classification-bar-height));
  /** The height of the classification bar */
  --classification-bar-height: 0px;
  --color-dark-background: rgba(0, 26, 51, 0.8);
  --color-dark-border: rgba(0, 26, 51, 0.8);
}

/* 系统缩放适配 - 通过JavaScript动态检测和设置 */
/* 默认缩放因子，将被JavaScript覆盖 */

/*Min Scrollbar for Webkit Browsers */

::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.5);
}

/*Hide yellow coloring in Chrome*/
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  /* 🔥 修改为透明背景，避免影响历史经度页面 */
  box-shadow: 0 0 0 30px transparent inset !important;
  -webkit-box-shadow: 0 0 0 30px transparent inset !important;
  -webkit-text-fill-color: white !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  transition: background-color 5000s ease-in-out 0s !important;
  animation: none !important;
}

input:-webkit-autofill {
  -webkit-text-fill-color: white !important;
  font-size: var(--font-size-md);
}

.pointable {
  cursor: pointer;
}

nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

a,
.link {
  cursor: pointer;
}

.link:hover {
  background: rgba(0, 0, 0, 0.2);
}

.eruda-icon-tool {
  display: none !important;
}

.eruda-entry-btn {
  display: none !important;
}

#keeptrack-root {
  margin: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  touch-action: none;
  -webkit-overflow-scrolling: none;
  /* Other browsers */
  overscroll-behavior: none;
}

#nav-wrapper {
  background: transparent;
  z-index: 3;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  height: var(--nav-bar-height);
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

#nav-footer-toggle {
  display: block;
  border-top-left-radius: calc(12px / var(--system-scale-factor));
  border-top-right-radius: calc(12px / var(--system-scale-factor));
  height: calc(20px / var(--system-scale-factor));
  width: calc(40px / var(--system-scale-factor));
  margin: 0px auto;
  margin-top: calc(-20px / var(--system-scale-factor));
  background: #2196f3;
  cursor: pointer;
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: bold;
}

#keeptrack-header {
  position: absolute;
  top: 0px;
  width: 100%;
  z-index: 105;
}

footer {
  padding-top: 0px !important; /* 移除顶部内边距 */
  position: fixed !important;
  top: auto !important;
  height: auto !important;
  margin-top: 0px !important;
  bottom: 0px !important;
  width: 100%;
  z-index: 100 !important;
  border: none !important; /* 移除边框 */
  background: transparent !important; /* 确保透明背景 */
}

#footer-handle {
  width: 100%;
  height: 5px;
  top: 0px;
  /* Make sure its drawn on top of the container for the slider button */
  z-index: 2;
  background-color: var(--color-dark-border) !important;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.huerotate {
  -webkit-filter: hue-rotate(180deg);
  filter: hue-rotate(180deg);
}

.center-text {
  text-align: center;
}

.vertcenter {
  line-height: 36px;
}

.text-select {
  -webkit-touch-callout: text;
  -webkit-user-select: text;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.no-text-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#classification-container {
  text-align: center;
  background: var(--classificationColor);
}

#keeptrack-canvas {
  background: var(--colorBlack);
  /* Inconsistent brightness without this */
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  touch-action: auto;
}

#canvas-holder {
  z-index: -2;
  padding: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

#load-cover {
  background: rgba(0, 0, 0, 0.8);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

#loader {
  background: var(--colorBlack);
  cursor: default;
  color: white;
  position: absolute;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 250px;
  height: 50px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  text-align: center;
}

.right-btn-menu {
  display: none;
  background: var(--color-dark-background);
  color: white;
  position: absolute;
  width: 165px;
  border-radius: 0px;
  border: 5px solid var(--color-dark-border);
  z-index: 10;
  /*Prevent fighting with the slide down button on mobile */
}

.right-btn-menu ul {
  width: 100%;
}

.right-btn-menu ul li {
  padding: 5px;
  min-height: 25px;
  cursor: pointer;
}

.right-btn-menu ul li:hover {
  background-color: var(--color-primary);
}

.right-btn-menu ul li:hover a {
  color: var(--colorWhite) !important;
  background-color: rgba(0, 0, 0, 0);
}

.rmb-menu-item:hover {
  background-color: var(--color-primary-dark) !important;
}

.rmb-menu-item a {
  color: white !important;
}

#sat-hoverbox {
  display: none;
  background: var(--color-dark-background) !important;
  cursor: default;
  color: white;
  position: absolute;
  text-align: center;
  padding: 10px;
  border-radius: 0px;
  border: none;
  pointer-events: none;
  z-index: 9999;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: center;
  row-gap: 10px;
}

#sat-minibox {
  font-size: 1.3vw;
}

.search-hilight {
  font-weight: bold;
  color: var(--color-dark-text-accent);
}

#search-results {
  transform: translateY(-100%);
  display: none;
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  width: 100%;
  height: auto; /* 🔥🔥🔥 自动高度，根据内容调整 */
  /* 🔥 半透明模糊背景效果 */
  background: rgba(0, 0, 0, 0.7) !important; /* 半透明黑色背景 */
  backdrop-filter: blur(15px) !important; /* 背景模糊效果 */
  -webkit-backdrop-filter: blur(15px) !important;
  z-index: 1;
  top: var(--top-menu-height);
  max-height: calc(100vh - var(--top-menu-height) - var(--bottom-menu-height)); /* 🔥🔥🔥 最大高度为屏幕高度减去顶部和底部菜单 */
  min-height: auto; /* 🔥🔥🔥 最小高度也自动调整 */
  /* 🔥 移除边框，使用半透明边框 */
  border: 1px solid rgba(255, 255, 255, 0.2) !important; /* 半透明白色边框 */
  border-radius: 8px !important; /* 添加圆角 */
  padding-top: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important; /* 阴影效果 */
}

/* 🔧 搜索结果响应式高度优化 - 自动调整大小 */
@media (max-height: 768px) {
  /* 笔记本屏幕 */
  #search-results {
    max-height: calc(100vh - var(--top-menu-height, 60px) - var(--bottom-menu-height, 120px)) !important;
    min-height: auto !important; /* 🔥🔥🔥 自动最小高度 */
  }
}

@media (max-height: 600px) {
  /* 小屏幕笔记本 */
  #search-results {
    max-height: calc(100vh - var(--top-menu-height, 60px) - var(--bottom-menu-height, 120px)) !important;
    min-height: auto !important; /* 🔥🔥🔥 自动最小高度 */
  }
}

@media (max-height: 480px) {
  /* 极小屏幕 */
  #search-results {
    max-height: calc(100vh - var(--top-menu-height, 60px) - var(--bottom-menu-height, 120px)) !important;
    min-height: auto !important; /* 🔥🔥🔥 自动最小高度 */
  }
}

.search-result {
  padding: 6px 10px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
}

.search-result:hover {
  background: rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.truncate-search {
  width: 280px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-item {
  cursor: pointer;
  z-index: 11;
}

.menu-item:first-child {
  margin-left: 2px;
}

.menu-item:hover .menu-title {
  color: var(--color-dark-background);
}

.menu-title {
  height: 30px;
}

.menu-radar {
  right: 50px;
}

.submenu {
  display: none;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  margin: 0;
  overflow: hidden;
  position: absolute;
}

#about-box {
  background: var(--colorBlack);
  right: 10px;
  width: 500px;
  height: 470px;
}

#about-content {
  font-size: 12px;
}

.box-header {
  font-size: 20px;
  margin-top: 5px;
  margin-bottom: 5px;
}

#version-box {
  background: transparent;
  right: 10px;
  width: 125px;
}

.title-text {
  text-align: center;
  margin-bottom: 5px;
  font-size: 18px;
  vertical-align: middle;
}

.status-box {
  background: rgba(0, 0, 0, 0);
  color: white;
  font-family: 'Open Sans', Times, serif;
  font-size: var(--font-size-xxl);
  position: absolute;
  height: calc(100px / var(--system-scale-factor)) !important;
  bottom: calc(50px / var(--system-scale-factor));
  z-index: 10002;
  width: 100%;
  text-align: center;
}

#camera-status-box {
  z-index: 10003;
  bottom: 100px;
}

#time-machine-menu li {
  display: flex;
}

.Square-Box {
  cursor: pointer;
  width: calc(30px / var(--system-scale-factor, 1));
  height: calc(30px / var(--system-scale-factor, 1));
  border-width: calc(2px / var(--system-scale-factor, 1));
  border-style: solid;
  border-radius: calc(15px / var(--system-scale-factor, 1));
  box-shadow:
    0 0px calc(6px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.2),
    0 0px calc(8px / var(--system-scale-factor, 1)) 0 rgba(0, 0, 0, 0.19);
  margin-right: calc(20px / var(--system-scale-factor, 1));
}

.legend-payload-box {
  background: rgb(68, 193, 47);
}

.legend-rocketBody-box {
  background: rgb(0, 48, 255);
}

.legend-debris-box {
  background: rgb(157, 157, 157);
}

.legend-sensor-box {
  background: rgb(166, 5, 5);
}

.legend-facility-box {
  background: rgb(108, 30, 103);
}

.legend-missile-box {
  background: rgb(200, 198, 9);
}

.legend-missileInview-box {
  background: rgb(255, 0, 0);
}

.legend-pink-box {
  background: rgb(108, 0, 50);
  display: hidden;
}

.legend-inFOV-box {
  background: rgb(255, 200, 0);
}

.legend-sensorCanObserve-box {
  background: rgb(255, 30, 0);
}

.legend-inviewAlt-box {
  background: rgb(255, 200, 0);
}

.legend-starLow-box {
  background: rgb(100, 100, 100);
}

.legend-starMed-box {
  background: rgb(150, 150, 150);
}

.legend-starHi-box {
  background: rgb(200, 200, 200);
}

.legend-satLow-box {
  background: rgb(100, 100, 100);
}

.legend-satMed-box {
  background: rgb(150, 150, 150);
}

.legend-satHi-box {
  background: rgb(200, 200, 200);
}

.legend-rcsSmall-box {
  background: rgb(100, 100, 100);
}

.legend-rcsMed-box {
  background: rgb(150, 150, 150);
}

.legend-rcsLarge-box {
  background: rgb(200, 200, 200);
}

.legend-rcsUnkown-box {
  background: rgb(200, 200, 200);
}

.legend-ageNew-box {
  background: rgb(100, 100, 100);
}

.legend-ageMed-box {
  background: rgb(150, 150, 150);
}

.legend-ageOld-box {
  background: rgb(200, 200, 200);
}

.legend-ageLost-box {
  background: rgb(200, 200, 200);
}

.legend-velocitySlow-box {
  background: rgb(100, 100, 100);
}

.legend-velocityMed-box {
  background: rgb(150, 150, 150);
}

.legend-velocityFast-box {
  background: rgb(200, 200, 200);
}

.legend-satLEO-box {
  background: rgb(200, 200, 200);
}

.legend-satGEO-box {
  background: rgb(200, 200, 200);
}

.legend-sourceUssf-box {
  background: rgb(11, 39, 131);
}

.legend-sourceAldoria-box {
  background: rgb(15, 150, 71);
}

.legend-sourceVimpel-box {
  background: rgb(204, 14, 14);
}

.legend-sourceCelestrak-box {
  background: rgb(206, 219, 14);
}

.legend-sourcePrismnet-box {
  background: rgba(168, 47, 199, 0.705);
}

#sensor-info-menu a {
  color: white !important;
}

.sensor-info-row {
  text-align: center;
  margin: 10px;
  height: 18px;
}

.sensor-info-key,
.sensor-info-value {
  display: inline-block;
  height: 18px;
}

.sensor-info-key {
  font-size: 12px;
  float: left;
}

.sensor-info-value {
  float: right;
  font-size: 12px;
}

.timeline-Header {
  display: none;
}

.remove-icon img {
  width: 25px;
  filter: invert(52%) sepia(130%) saturate(1000%) hue-rotate(210deg) brightness(100%) contrast(86%);
}

.add-icon img {
  padding-top: 35px;
  width: 25px;
  filter: hue-rotate(210deg) brightness(1.2);
}

#iss-stream-menu {
  display: none;
  position: absolute;
  width: 270px;
  height: 620px;
  z-index: 10;
  margin-left: -15px;
}

#iss-stream-menu iframe {
  width: 285px;
  margin-bottom: -6px;
}

#launch-menu {
  display: none;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  width: 1160px;
  max-height: 100%;
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 10;
  left: 0;
  top: 60px;
  bottom: 60px;
  overflow: auto;
  color: white;
}

.btn-green {
  background-color: rgb(86, 240, 0) !important;
}

.btn-red {
  background-color: rgb(255, 56, 56) !important;
}

.btn-red:hover {
  background-color: rgb(255, 0, 0) !important;
}

.btn-red:active {
  background-color: rgb(255, 0, 0) !important;
}

#looks {
  margin: 0 auto;
}

#lookangles-content td {
  padding: 5px;
  font-size: 13px;
}

.start-hidden {
  display: none;
}

#editor-menu {
  background: var(--color-dark-background);
  color: white;
  overflow: auto;
}

#es-error,
#ms-error {
  margin-top: 10px;
  padding-bottom: 10px;
  cursor: 'pointer';
}

#info-overlay-menu {
  background: rgba(0, 0, 0, 0);
  width: 0px;
  height: auto;
  bottom: auto;
}

#info-overlay-content {
  position: absolute;
  background: rgba(0, 0, 0, 0);
  color: white;
  max-width: 550px;
  padding-left: 20px;
  top: 80px;
  height: auto;
  overflow: auto;
  z-index: 10;
}

/** Stylize each row in the transparent overlay */
#info-overlay-content>div>div {
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.5);
  margin-bottom: 10px;
}

#info-overlay-content>div>div>h5 {
  margin: 0;
}

.plot-analysis-menu-maximized {
  width: 100% !important;
  height: 100% !important;
}

.plot-analysis-menu-normal {
  width: calc(1300px / var(--system-scale-factor, 1));
  z-index: 2;
}

.plot-analysis-chart {
  width: 100%;
  height: 90%;
  padding-bottom: 80px;
  padding-top: 10px;
  margin: 0;
}

.side-menu-row-header {
  background: var(--color-dark-border);
  margin: 0px -5px;
  padding: 5px;
}

.flow5out {
  margin: 0px -5px;
}

.side-menu-parent {
  display: none;
  position: absolute;
  width: calc(300px / var(--system-scale-factor));
  min-width: calc(280px / var(--system-scale-factor));
  max-width: calc(600px / var(--system-scale-factor));
  top: var(--top-menu-height);
  bottom: var(--bottom-menu-top);
  left: 0px;
  transition: all 1s ease-in-out;
  transform: translateX(-100%);
  background: transparent !important; /* 强制透明背景 */
  background-color: transparent !important;
  background-image: none !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: none !important; /* 移除边框 */
  box-shadow: none !important;
  z-index: 1000;
}

.divider {
  padding: 2px !important;
  background-color: var(--color-dark-background);
}

input[type='checkbox'].css-checkbox {
  position: absolute;
  z-index: -1000;
  left: -1000px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  height: 1px;
  width: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
}

input[type='checkbox'].css-checkbox+label.css-label {
  padding-left: 25px;
  height: 20px;
  display: inline-block;
  line-height: 20px;
  background-repeat: no-repeat;
  background-position: 0px 0px;
  font-size: 16px;
  vertical-align: middle;
  cursor: pointer;
}

input[type='checkbox'].css-checkbox:checked+label.css-label {
  background-position: 0px -20px;
}

label.css-label {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

fieldset {
  display: block;
  -webkit-margin-start: 0px;
  -webkit-margin-end: 0px;
  -webkit-padding-before: 0em;
  -webkit-padding-start: 0em;
  -webkit-padding-end: 0em;
  -webkit-padding-after: 0em;
  border: 0px;
  border-image-source: initial;
  border-image-slice: initial;
  border-image-width: initial;
  border-image-outset: initial;
  border-image-repeat: initial;
  min-width: -webkit-min-content;
  padding: 25px 30px;
}

.ghost-input {
  text-align: right;
  font-weight: 300;
  width: 120px;
  position: absolute;
  left: 140px;
  padding: 10px;
  border: 0px;
  outline: none;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  color: var(--colorWhite);
  background: rgba(0, 0, 0, 0);
  font-family:
    Open Sans,
    Verdana,
    sans-serif;
  -webkit-transition: all 0.1s ease-in-out;
  -moz-transition: all 0.1s ease-in-out;
  -ms-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}

.ghost-input:focus {
  border-bottom: 1px solid #ddd;
}

.ghost-input:hover {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5px;
}

.ghost-button {
  display: block;
  border: 2px white;
  background: var(--colorWhite);
  padding: 10px;
  width: 100%;
  margin: 20px auto;
  -webkit-transition: all 0.1s ease-in-out;
  -moz-transition: all 0.1s ease-in-out;
  -ms-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}

.ghost-button:hover {
  background: #c33;
  color: var(--colorWhite);
}

a {
  text-decoration: none;
  color: var(--color-dark-text-accent) !important;
}

ul li {
  list-style: none;
  padding: 5px;
}

#colorbox {
  border: 10px solid var(--color-dark-background);
  z-index: 10;
  box-sizing: content-box;
}

#colorbox-header {
  background: var(--color-dark-border);
  width: 100%;
  height: 34px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding-right: 15px;
  padding-bottom: 10px;
}

#colorbox-header button {
  width: 24px;
  height: 24px;
  cursor: pointer;
  border: 0px;
  background: none;
  color: white;
}

#cboxLoadedContent {
  overflow: hidden !important;
  background: var(--colorWhite);
}

.cboxIframe {
  width: 100%;
  height: 100%;
}

.satPhotoRow {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.ui-priority-secondary {
  font-weight: bold !important;
  opacity: 1 !important;
}

/* 搜索框清除按钮 */
#clear-search {
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: calc(100% - 4px);
  background: rgba(0, 0, 0, 0.3);
  border: none;
  border-radius: 0 4px 4px 0;
  color: white;
  font-size: 16px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  outline: none;
}

#clear-search:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 顶部菜单日期窗口设置*/
#jday {
  font-family: 'Roboto Mono', monospace;
  font-size: var(--font-size-xl);
  width: calc(200px / var(--system-scale-factor));
  min-width: calc(200px / var(--system-scale-factor));
  max-width: calc(200px / var(--system-scale-factor));
}

#sensor-selected {
  font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
  font-size: var(--font-size-xl);
  padding: 0px calc(10px / var(--system-scale-factor));
  display: inline-block;
  width: auto;
  min-width: calc(150px / var(--system-scale-factor));
  max-width: calc(600px / var(--system-scale-factor));
  flex-shrink: 0;
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
  box-sizing: content-box;
}

#datetime {
  font-size: var(--font-size-sm);
  text-align: center;
}

#datetime-input {
  position: absolute;
  bottom: 10px;
  display: none;
}

#datetime-input-tb {
  color: white;
  font-family: 'Open Sans', sans-serif;
  /* Hides it above the top of the screen */
  height: 0px !important;
  margin: 0 0 55px 0 !important;
}

#datetime-input-tb:focus,
input:focus {
  outline: none;
}

#datetime-title {
  position: absolute;
  left: 20px;
  bottom: 20px;
  width: 220px;
  height: 30px;
  background: transparent;
  overflow: hidden;
  text-align: center;
  font-weight: bold;
  font-size: 12px;
}

#datetime-text {
  font-family: 'Roboto Mono', monospace;
  font-size: var(--font-size-xl);
  cursor: pointer;
  border-width: 0px;
}

#sensor-selected-container {
  width: auto !important;
  min-width: 100px;
  max-width: none;
}

#nav-mobile {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
  font-size: 20px;
  font-family: 'Roboto Mono', monospace;
  margin-left: 10px;
}

#nav-mobile2 {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  gap: 5px;
  /* 减少列表项间距 */
}

#nav-mobile2>li {
  margin: 0;
  padding: 0;
}

#nav-mobile2 a:hover {
  filter: brightness(3);
}

#sensor-selected-container:hover,
#datetime-text:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 非实时指示器样式 */
#non-realtime-indicator {
  background-color: #ffeb3b !important;
  color: #333 !important;
  padding: calc(2px / var(--system-scale-factor)) calc(8px / var(--system-scale-factor));
  border-radius: calc(3px / var(--system-scale-factor));
  font-size: var(--font-size-sm);
  font-weight: bold;
  cursor: pointer;
  margin-left: calc(5px / var(--system-scale-factor));
  font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
  transition: background-color 0.3s ease;
}

#non-realtime-indicator:hover {
  background-color: #fdd835 !important;
}

#sensor-selected-container {
  display: none;
  width: 0px;
  cursor: pointer;
  padding: 0px;
  transition: none;
  -webkit-transition: none;
}

#ui-datepicker-div {
  border: none;
  top: var(--top-menu-height);
  left: 0px;
  width: 100%;
  border-radius: 0px;
}

.ui-datepicker-calendar {
  margin-bottom: 0px;
}

.ui-datepicker-calendar tbody tr {
  border: 0px !important;
}

.ui-datepicker-calendar thead tr {
  border: 0px !important;
}

.ui-datepicker-calendar tbody tr td a {
  display: flex;
  font-weight: bold;
  color: var(--color-primary);
  background: var(--color-dark-background);
  border: calc(1px / var(--system-scale-factor, 1)) solid rgba(0, 0, 0, 0);
  height: calc(45px / var(--system-scale-factor, 1));
  width: calc(45px / var(--system-scale-factor, 1));
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
}

.ui-datepicker-calendar tbody tr td a:hover {
  border: 1px solid rgb(230, 230, 230) !important;
  background: rgba(230, 230, 230, 0.15) !important;
  color: var(--colorWhite) !important;
  border-radius: 3px;
}

.ui-datepicker .ui-datepicker-buttonpane button {
  float: right;
  margin: 0.5em 0.2em 0.4em;
  cursor: pointer;
  width: auto;
  overflow: visible;
  padding: 1em;
}

.ui-datepicker td a {
  padding: 15px 0.2em;
}

#bottom-icons-container {
  width: 100% !important;
  height: var(--bottom-menu-height);
  overflow: hidden;
  text-align: left;
}

#bottom-icons-filter {
  width: var(--bottom-filter-width);
  height: var(--bottom-menu-height);
  float: left;
  display: flex;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  background: transparent;
  z-index: 11;
  position: absolute;
  flex-direction: column;
  align-content: center;
  align-items: flex-start;
  justify-content: flex-start;
  overflow-y: scroll;
}

#bottom-icons {
  width: calc(100% - var(--bottom-filter-width));
  float: right;
  display: grid;
  grid-template-columns: repeat(auto-fill, var(--bottom-icon-width));
  justify-content: center;
  row-gap: 5px;
  background: transparent;
  padding-top: 10px;
  padding-bottom: 10px;
  z-index: 11;
  overflow-y: auto;
  max-height: var(--bottom-menu-height);
}

.bmenu-item {
  padding: 5px;
  text-align: center;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  height: 100%;
  max-width: var(--bottom-icon-width);
}

.bmenu-filter-item {
  padding: 2px;
  text-align: center;
  cursor: pointer;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  align-content: center;
  justify-content: center;
  align-items: center;
}


.bmenu-item-inner {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  margin-bottom: 5px;
}

.bmenu-item .status-icon {
  width: 15px;
  height: 15px;
  top: -20px;
  background-size: cover;
  position: relative;
  z-index: 5;
  left: 15px;
  display: block;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABhGlDQ1BJQ0MgcHJvZmlsZQAAKJF9kTtIw1AUhv+mFUUqDmYQcchQnSyILxylikWwUNoKrTqY3PQFTRqSFBdHwbXg4GOx6uDirKuDqyAIPkBcXZwUXaTEc5NCixgvHO7Hf+//c+65gNCoMM0KjQOabpupeEzK5lal7leEIVJNIyQzy0ikFzPwXV/3CPD9Lsqz/O/9ufrUvMWAgEQ8xwzTJt4gntm0Dc77xCIrySrxOfGYSQ0SP3Jd8fiNc9FlgWeKZiY1TywSS8UOVjqYlUyNeIo4omo65QtZj1XOW5y1So21+uQvDOf1lTTXqYYRxxISSEKCghrKqMBGlHadFAspOo/5+Idcf5JcCrnKYORYQBUaZNcP/ge/Z2sVJie8pHAM6HpxnI8RoHsXaNYd5/vYcZonQPAZuNLb/moDmP0kvd7WIkdA/zZwcd3WlD3gcgcYfDJkU3alIJVQKADvZ/RNOWDgFuhd8+bWOsfpA5ChWS3fAAeHwGiRstd93t3TObd/77Tm9wOOiHKymlTPuAAAAAZiS0dEAM4AowCKeTVkwgAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB+cMFAw5A0OJiTcAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAB20lEQVRYw+2WvU4bURCFv7m2NpUlaiSngcLF2hLOG0BHYb9ACoqkg44+ovcjWIKCF7ALOvIGBuKlSBGaIFEjucoK30kRW4p8Z9e7xJKR4ml35tzZM2d+YGP/u0kZ5zjRuqR0BQ5RdlTYBhDlCeFB4UojBvdNeVxpAns3uj31nCl8Kgjarzi+3Lbl6Z8TaN1oRz2XQK0kuxNxfBy3ZZjn5PI+Nkd6rJ7BKx4HqKln0Bzp8asYmP35YCVCc3SzmJCsmr94vmf8+cRBTyoMtyJ+ADyn7OqUjofTrJiqo2FpomolMPWcWUAifPURR99Cld8Bd3Gi5y7lQpX9xXLMMD8vZSBOtC6/+Gk9Pv4gB0Uob4302kgCfcf7xRYNRCgpXYtCH3FUtOYz30kRbGdQcmg49coMl/umPDroFcRe5ImdILDCsLTyrRgDO0hgPl7/trnay5gVY2G7dS+jUANK0KvPKbtlga0YC9sZo+khoG5Kp2wCZoyBHWoAroK2gtM40XqZtT2bikuxwwQic/7XXMpF4br+8a0VwXZWDwv0g2BlvzXS6zwm4kTrWVNQoG/NkrUvo7Wv48w5MG7LEOFkBVfnSd5V9LZPsjkTVUfDEmbeUVp1NJY9/ibO8o1t7Ddz5991OpoAtwAAAABJRU5ErkJggg==');
}

.bmenu-item .bmenu-item-inner img {
  width: var(--bottom-icon-img-size);
  height: auto;
  margin: 0 auto;
  display: block;
}

.bmenu-filter-item .bmenu-filter-item-inner img {
  width: 25px;
  height: auto;
  margin: 0 auto;
  display: block;
  padding-bottom: 3px;
}

#github-share1 img,
#legend-icon img,
#tutorial-icon img,
#fullscreen-icon img,
#search-icon img,
.bmenu-item:not(.bmenu-item-disabled, .bmenu-item-selected, .bmenu-item-help) .bmenu-item-inner *,
.bmenu-filter-item:not(.bmenu-item-disabled, .bmenu-item-selected, .bmenu-item-help) .bmenu-filter-item-inner * {
  /* Convert blue to engineering red */
  filter: hue-rotate(0deg) brightness(0.7) saturate(4);
}

.bmenu-item:hover,
.bmenu-filter-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
}

.bmenu-item .bmenu-item-inner:active *,
.bmenu-filter-item .bmenu-filter-item-inner:active * {
  scale: 0.9;
}

.bmenu-title {
  position: relative;
  display: block;
  font-size: var(--font-size-base);
  text-align: center;
  text-overflow: ellipsis;
  overflow-x: hidden;
  word-wrap: break-word;
}

.bmenu-filter-title {
  position: relative;
  display: block;
  font-size: var(--font-size-base);
  text-align: center;
  padding-left: calc(3px / var(--system-scale-factor));
}

.bmenu-item-selected img {
  filter: hue-rotate(210deg) brightness(1.2);
}

.bmenu-item-selected .status-icon {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABhGlDQ1BJQ0MgcHJvZmlsZQAAKJF9kTtIw1AUhv+mFUUqDmYQcchQnSyILxylikWwUNoKrTqY3PQFTRqSFBdHwbXg4GOx6uDirKuDqyAIPkBcXZwUXaTEc5NCixgvHO7Hf+//c+65gNCoMM0KjQOabpupeEzK5lal7leEIVJNIyQzy0ikFzPwXV/3CPD9Lsqz/O/9ufrUvMWAgEQ8xwzTJt4gntm0Dc77xCIrySrxOfGYSQ0SP3Jd8fiNc9FlgWeKZiY1TywSS8UOVjqYlUyNeIo4omo65QtZj1XOW5y1So21+uQvDOf1lTTXqYYRxxISSEKCghrKqMBGlHadFAspOo/5+Idcf5JcCrnKYORYQBUaZNcP/ge/Z2sVJie8pHAM6HpxnI8RoHsXaNYd5/vYcZonQPAZuNLb/moDmP0kvd7WIkdA/zZwcd3WlD3gcgcYfDJkU3alIJVQKADvZ/RNOWDgFuhd8+bWOsfpA5ChWS3fAAeHwGiRstd93t3TObd/77Tm9wOOiHKymlTPuAAAAAZiS0dEAM4AowCKeTVkwgAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB+cMFAw5C01SAQUAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAABd0lEQVRYw+2WsWoCQRCGv1m5K0R8gAhpbHyXgJhCOLDLAwi+wT2I2AUEC0W4d0ljEzC9IhYe7qS5NHHPPfWOCHHqZf5/vllmBh7x30Muedzf06ql9BRegLYqTwAifAErgeQYsJjVWZdqoL+nZQ7EKryhGE9GK8rEhsRFjHgNRFu61vIONC6kuzOGwbTJ8tyjs9VEG4ZWmV8hDtCwyjzaMLyKQLSla5W5F7mfsTXCax4Jyeu5HPi4snJnOzSk4/oTzurMgbhEcYBGltNPoL+nJSmfN6N3tEIDnn9TOBGppfRKFwdQTC2l521BNmQqCVduV6XtCidv208gG6+VEHDkNvxxnBjIFks1m8+R20VgVWHBKz8BSCrc/YnXwDFggWArULfHgIXXwKzOWpRJ6frKpPAusCExsCtRf5flLHYPzOqsjWFQSisEawyDvOsodw5MmywNjG4yIVgDo3NX0X2fZD8kNKQjMC5EQ7ACYw3p+MTv4ix/xCO+AfaWiX0YOWcHAAAAAElFTkSuQmCC');
}

.bmenu-item-selected:hover img {
  filter: hue-rotate(210deg) brightness(1.8);
}

.bmenu-item-selected:hover .status-icon {
  filter: brightness(1.8);
}

.bmenu-item-help img {
  filter: hue-rotate(227deg) brightness(1.2);
}

.bmenu-item-help .status-icon {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABhGlDQ1BJQ0MgcHJvZmlsZQAAKJF9kTtIw1AUhv+mFUUqDmYQcchQnSyILxylikWwUNoKrTqY3PQFTRqSFBdHwbXg4GOx6uDirKuDqyAIPkBcXZwUXaTEc5NCixgvHO7Hf+//c+65gNCoMM0KjQOabpupeEzK5lal7leEIVJNIyQzy0ikFzPwXV/3CPD9Lsqz/O/9ufrUvMWAgEQ8xwzTJt4gntm0Dc77xCIrySrxOfGYSQ0SP3Jd8fiNc9FlgWeKZiY1TywSS8UOVjqYlUyNeIo4omo65QtZj1XOW5y1So21+uQvDOf1lTTXqYYRxxISSEKCghrKqMBGlHadFAspOo/5+Idcf5JcCrnKYORYQBUaZNcP/ge/Z2sVJie8pHAM6HpxnI8RoHsXaNYd5/vYcZonQPAZuNLb/moDmP0kvd7WIkdA/zZwcd3WlD3gcgcYfDJkU3alIJVQKADvZ/RNOWDgFuhd8+bWOsfpA5ChWS3fAAeHwGiRstd93t3TObd/77Tm9wOOiHKymlTPuAAAAAZiS0dEAM4AowCKeTVkwgAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB+cMFAw5HM6BhMIAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAAPklEQVRYw2NgGAUjHTCiC/x5YfWflhaySBxDsZNpoENg1AGjDhh1wKgDRh0w6oBRB4w6YNQBow4YdcAoGAUAZVMEMGAOxt0AAAAASUVORK5CYII=');
}

.bmenu-item-help:hover img {
  filter: hue-rotate(227deg) brightness(1.8);
}

.bmenu-item-help:hover .status-icon {
  filter: brightness(1.8);
}

.bmenu-item-disabled img,
.bmenu-item-disabled img * {
  filter: grayscale(1) !important;
}

.bmenu-item-disabled .status-icon {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABhGlDQ1BJQ0MgcHJvZmlsZQAAKJF9kTtIw1AUhv+mFUUqDmYQcchQnSyILxylikWwUNoKrTqY3PQFTRqSFBdHwbXg4GOx6uDirKuDqyAIPkBcXZwUXaTEc5NCixgvHO7Hf+//c+65gNCoMM0KjQOabpupeEzK5lal7leEIVJNIyQzy0ikFzPwXV/3CPD9Lsqz/O/9ufrUvMWAgEQ8xwzTJt4gntm0Dc77xCIrySrxOfGYSQ0SP3Jd8fiNc9FlgWeKZiY1TywSS8UOVjqYlUyNeIo4omo65QtZj1XOW5y1So21+uQvDOf1lTTXqYYRxxISSEKCghrKqMBGlHadFAspOo/5+Idcf5JcCrnKYORYQBUaZNcP/ge/Z2sVJie8pHAM6HpxnI8RoHsXaNYd5/vYcZonQPAZuNLb/moDmP0kvd7WIkdA/zZwcd3WlD3gcgcYfDJkU3alIJVQKADvZ/RNOWDgFuhd8+bWOsfpA5ChWS3fAAeHwGiRstd93t3TObd/77Tm9wOOiHKymlTPuAAAAAZiS0dEAM4AowCKeTVkwgAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB+cMFAw4OOuZUVIAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAA5UlEQVRYw+2VLRKDMBCF3zYShcMhepMimKlhisBhcggOwSEwuIoyNcxUlJsg4nAoJJOKUtefTScdKvLpvOzLvp0N4HA4HCtDpoLhknvteCgISDT09n4J9Ro47/2mDOJ6+pmB6njaYdYVgPDFEQVBUmZpZ93AUvzKOiwo4prYcNu+vJzHrKvhknvWDLTjoXjT9meEi8aOAQKSL6Y7sWbgMe0mcDWbtfcAMwLqzSPgaZgR4GweAU/DMrD3mxKAMqivFo0dA0FcTxAk2eUFSe5KZg+hzNIOgqIPnVAmW/AvPiOHw+FYnRsr7lD5IAODJgAAAABJRU5ErkJggg==');
}

.bmenu-item-error img {
  filter: invert(52%) sepia(130%) saturate(1000%) hue-rotate(210deg) brightness(100%) contrast(86%);
}

.bmenu-item-error .status-icon {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABhGlDQ1BJQ0MgcHJvZmlsZQAAKJF9kTtIw1AUhv+mFUUqDmYQcchQnSyILxylikWwUNoKrTqY3PQFTRqSFBdHwbXg4GOx6uDirKuDqyAIPkBcXZwUXaTEc5NCixgvHO7Hf+//c+65gNCoMM0KjQOabpupeEzK5lal7leEIVJNIyQzy0ikFzPwXV/3CPD9Lsqz/O/9ufrUvMWAgEQ8xwzTJt4gntm0Dc77xCIrySrxOfGYSQ0SP3Jd8fiNc9FlgWeKZiY1TywSS8UOVjqYlUyNeIo4omo65QtZj1XOW5y1So21+uQvDOf1lTTXqYYRxxISSEKCghrKqMBGlHadFAspOo/5+Idcf5JcCrnKYORYQBUaZNcP/ge/Z2sVJie8pHAM6HpxnI8RoHsXaNYd5/vYcZonQPAZuNLb/moDmP0kvd7WIkdA/zZwcd3WlD3gcgcYfDJkU3alIJVQKADvZ/RNOWDgFuhd8+bWOsfpA5ChWS3fAAeHwGiRstd93t3TObd/77Tm9wOOiHKymlTPuAAAAAZiS0dEAM4AowCKeTVkwgAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB+cMFAw5JOaDPFwAAAAZdEVYdENvbW1lbnQAQ3JlYXRlZCB3aXRoIEdJTVBXgQ4XAAAA0UlEQVRYw+3WSw7DIAwEUKbppVi1R+vR0lWORbdVgj9jsKpKeImwo3kiJKWsWvXjwnmh1bqX1h45T8Mbx/H8XrpdNm3bKy1uZzZ6+1IUOun7AlkKwkxI+6cqCOllgdkKyixofVMUlPS6wCwFYwas/iEFI70tMKrg6IVnTkjBkd4nEFVw9sA7j1JwpvcLsArEXjCqLgUiPSfgTUaeF7BnS1Ug0/MCVsLA24LI/dJVCKSPCUhJgzdmSOCiEEwfFzgnzvyPtBRarfvIjHvmt37VX9QHmaFdkiJXncIAAAAASUVORK5CYII=');
}

#custom-sensor-menu-settings-btn:active {
  scale: 0.8;
}

#down {
  margin: 0px auto;
}

.ui_tpicker_time_input {
  font-family: 'Roboto Mono', monospace !important;
  height: 15px !important;
  border-bottom: 0px !important;
  text-align: center !important;
  font-size: 20px !important;
}

.ui_tpicker_hour,
.ui_tpicker_minute,
.ui_tpicker_second,
.ui_tpicker_proprate {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-direction: row;
  flex-wrap: nowrap;
}

#editorForm label {
  top: 20px;
  font-size: 1em;
}

#editorForm div {
  margin-top: 2em;
}

#editor-LS-menu label {
  top: -25px;
  font-size: 1em;
}

#editor-LS {
  display: block !important;
  background: steelblue;
  height: 2em;
}

#logo-primary {
  position: absolute;
  top: calc(var(--top-menu-height) + 10px);
  left: 10px;
  height: auto;
  width: 4vw;
  z-index: 0;
  max-width: 150px;
  min-width: 70px;
}

#logo-primary img {
  width: 100%;
  height: 100%;
}

#version-text {
  font-family: var(--font-family);
  position: absolute;
  left: calc(12px / var(--system-scale-factor, 1));
  top: calc(12px / var(--system-scale-factor, 1));
  width: 100%;
  text-align: left;
  font-size: calc(14px / var(--system-scale-factor, 1));
  color: white;
  pointer-events: none;
  background: none;
  z-index: 1000;
  text-shadow: 0 0px calc(2px / var(--system-scale-factor, 1)) rgba(0, 0, 0, 1), 0 0px calc(2px / var(--system-scale-factor, 1)) #000;
}

#copyright-notice {
  font-family: var(--font-family);
  position: absolute;
  left: 0;
  bottom: calc(12px / var(--system-scale-factor, 1));
  width: 100%;
  text-align: center;
  font-size: calc(10px / var(--system-scale-factor, 1));
  color: white;
  pointer-events: none;
  background: none;
  z-index: 1000;
  text-shadow: 0 0px calc(2px / var(--system-scale-factor, 1)) rgba(0, 0, 0, 1), 0 0px calc(2px / var(--system-scale-factor, 1)) #000;
}

#logo-secondary {
  position: absolute;
  top: calc(var(--top-menu-height) + 10px);
  left: calc(20px + max(4vw, 70px));
  height: auto;
  width: 4vw;
  z-index: 0;
  max-width: 150px;
  min-width: 70px;
}

#logo-secondary img {
  width: 100%;
  height: 100%;
}

#colors-menu ul li {
  text-align: center;
  padding: 2px !important;
}

#external-menu ul li {
  text-align: center;
  padding: 2px !important;
}

#analysis-menu ul li {
  text-align: center;
  padding: 2px !important;
}

#country-menu ul li {
  text-align: center;
  padding: 2px !important;
}

#constellation-menu ul li {
  text-align: center;
  padding: 2px !important;
}

.menu-selectable:hover {
  background: rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.dark-blue-badge {
  color: var(--color-dark-text-accent) !important;
  font-weight: bold;
}

.dark-gray-badge {
  color: var(--statusLightOff) !important;
  font-weight: bold;
}

.ui_tpicker_time {
  height: 31px;
  text-align: center;
}

.dropdown-content {
  max-height: 60vh !important;  /* 增加下拉窗口高度 */
}

/* scroll bar coloring under .dropdown-content */
.dropdown-content::-webkit-scrollbar-thumb {
  background-color: var(--color-dark-text-accent) !important;
}

.dropdown-content::-webkit-scrollbar-track {
  background-color: transparent !important;
}

#help-screen {
  display: flex;
  max-width: 80%;
  max-height: 80%;
  flex-shrink: 1;
  position: relative;
  inset: 10%;
  z-index: 99;
  border: 5px solid var(--color-dark-border);
  text-align: center;
  padding: 10px;
  overflow: auto;
  background: var(--color-dark-background);
}

#help-outer-container {
  display: none;
  background-color: rgba(0, 0, 0, 0.75);
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 99;
}

#help-inner-container {
  margin: auto;
  width: 100%;
}

#help-header {
  font-size: 1.5em;
}

.help-header-sel:hover {
  cursor: pointer;
  color: var(--color-dark-text-accent);
}

#help-close {
  position: absolute;
  right: 15px;
  top: 5px;
  font-size: 2em;
}

#help-close:hover {
  cursor: pointer;
  color: var(--color-dark-text-accent);
}

#help-text {
  display: block;
  line-height: 20px;
  text-align: left;
}

.top-menu-icons-search-on {
  transition: 1s;
}

/* *********** Calendar CSS *********** */
.ui-widget-content {
  background: var(--color-dark-background);
  border: 1px solid var(--colorTertiary);
  font-family: 'Open Sans', sans-serif;
  color: var(--colorWhite);
  z-index: 25;
}

.ui-state-default {
  border: 1px solid var(--colorTertiary);
  background: var(--colorTertiary);
  color: var(--colorWhite);
}

.ui-state-highlight {
  border: 1px solid rgb(107, 107, 107) !important;
  background: rgba(107, 107, 107, 0.15) !important;
  color: var(--colorWhite) !important;
  border-radius: 3px;
}

.ui-state-active {
  border: 1px solid #ff9800 !important; /* 橙色边框 */
  background: rgba(255, 152, 0, 0.8) !important; /* 橙色背景 */
  color: var(--colorWhite) !important;
  border-radius: 3px;
}

.ui-widget-header {
  font-family: 'Open Sans', sans-serif;
  color: var(--colorWhite);
  font-weight: bold;
}

.ui-button {
  border: 1px solid var(--colorTertiary);
  background: var(--colorTertiary);
  padding: 0.4em 1em;
  display: inline-block;
  position: relative;
  line-height: normal;
  margin-right: 0.1em;
  cursor: pointer;
  vertical-align: middle;
  text-align: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  overflow: visible;
}

.ui-button span {
  filter: brightness(5);
}

.ui-icon-circle-triangle-e {
  background-position: -33px -17px;
  filter: brightness(1.4) sepia(1) hue-rotate(-46deg) saturate(7.5);
}

.ui-icon-circle-triangle-w {
  background-position: -97px -17px;
  filter: brightness(1.4) sepia(1) hue-rotate(-46deg) saturate(7.5);
}

.ui-slider-handle {
  border-radius: 10px !important;
  background: var(--color-primary);
  border: 2px solid white;
}

.ui-timepicker-div .ui-widget-header {
  margin-bottom: 8px;
}

.ui-timepicker-div dl {
  margin-top: 0px;
  text-align: left;
}

.ui-timepicker-div dl dt {
  float: left;
  clear: left;
  padding: 0 0 0 5px;
}

.ui-timepicker-div dl dd {
  margin: 0 5px 10px 30%;
}

.ui-timepicker-div td {
  font-size: 90%;
}

.ui-tpicker-grid-label {
  background: none;
  border: none;
  margin: 0;
  padding: 0;
}

.ui-timepicker-div .ui_tpicker_unit_hide {
  display: none;
}

.ui-timepicker-div .ui_tpicker_time .ui_tpicker_time_input {
  background: none;
  color: inherit;
  border: none;
  outline: none;
  border-bottom: 0px !important;
  width: 95%;
  height: 15px;
  text-align: center;
}

.ui-timepicker-div .ui_tpicker_time .ui_tpicker_time_input:focus {
  border-bottom-color: #aaa;
}

.ui-timepicker-rtl {
  direction: rtl;
}

.ui-timepicker-rtl dl {
  text-align: right;
  padding: 0 5px 0 0;
}

.ui-timepicker-rtl dl dt {
  float: right;
  clear: right;
}

.ui-timepicker-rtl dl dd {
  margin: 0 40% 10px 10px;
}

.ui-timepicker-div.ui-timepicker-oneLine {
  padding-right: 2px;
}

.ui-timepicker-div.ui-timepicker-oneLine .ui_tpicker_time,
.ui-timepicker-div.ui-timepicker-oneLine dt {
  display: none;
}

.ui-timepicker-div.ui-timepicker-oneLine .ui_tpicker_time_label {
  display: block;
  padding-top: 2px;
}

.ui-timepicker-div.ui-timepicker-oneLine dl {
  text-align: right;
}

.ui-timepicker-div.ui-timepicker-oneLine dl dd,
.ui-timepicker-div.ui-timepicker-oneLine dl dd>div {
  display: inline-block;
  margin: 0;
}

.ui-timepicker-div.ui-timepicker-oneLine dl dd.ui_tpicker_minute:before,
.ui-timepicker-div.ui-timepicker-oneLine dl dd.ui_tpicker_second:before {
  content: ':';
  display: inline-block;
}

.ui-timepicker-div.ui-timepicker-oneLine dl dd.ui_tpicker_millisec:before,
.ui-timepicker-div.ui-timepicker-oneLine dl dd.ui_tpicker_microsec:before {
  content: '.';
  display: inline-block;
}

.ui-timepicker-div.ui-timepicker-oneLine .ui_tpicker_unit_hide,
.ui-timepicker-div.ui-timepicker-oneLine .ui_tpicker_unit_hide:before {
  display: none;
}

.ui-helper-hidden {
  display: none;
}

.ui-helper-hidden-accessible {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.ui-helper-reset {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  line-height: 1.3;
  text-decoration: none;
  font-size: 100%;
  list-style: none;
}

.ui-helper-clearfix:before,
.ui-helper-clearfix:after {
  content: '';
  display: table;
  border-collapse: collapse;
}

.ui-helper-clearfix:after {
  clear: both;
}

.ui-helper-zfix {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  opacity: 0;
  filter: Alpha(Opacity=0);
}

.ui-front {
  z-index: 100;
}

.ui-state-disabled {
  cursor: default !important;
  pointer-events: none;
}

.ui-icon {
  display: inline-block;
  vertical-align: middle;
  margin-top: -0.25em;
  position: relative;
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
  width: 16px;
  height: 16px;
}

.ui-widget-icon-block {
  left: 50%;
  margin-left: -8px;
  display: block;
}

.ui-widget-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #aaa;
  opacity: 0.003;
  filter: Alpha(Opacity=.3);
}

.ui-accordion .ui-accordion-header {
  display: block;
  cursor: pointer;
  position: relative;
  margin: 2px 0 0 0;
  padding: 0.5em 0.5em 0.5em 0.7em;
  font-size: 100%;
}

.ui-accordion .ui-accordion-content {
  padding: 1em 2.2em;
  border-top: 0;
  overflow: auto;
}

.ui-autocomplete {
  position: absolute;
  top: 0;
  left: 0;
  cursor: default;
}

.ui-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: block;
  outline: 0;
}

.ui-menu .ui-menu {
  position: absolute;
}

.ui-menu .ui-menu-item {
  margin: 0;
  cursor: pointer;
  list-style-image: url('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
}

.ui-menu .ui-menu-item-wrapper {
  position: relative;
  padding: 3px 1em 3px 0.4em;
}

.ui-menu .ui-menu-divider {
  margin: 5px 0;
  height: 0;
  font-size: 0;
  line-height: 0;
  border-width: 1px 0 0 0;
}

.ui-menu .ui-state-focus,
.ui-menu .ui-state-active {
  margin: -1px;
}

.ui-menu-icons {
  position: relative;
}

.ui-menu-icons .ui-menu-item-wrapper {
  padding-left: 2em;
}

.ui-menu .ui-icon {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0.2em;
  margin: auto 0;
}

.ui-menu .ui-menu-icon {
  left: auto;
  right: 0;
}

.ui-button,
.ui-button:link,
.ui-button:visited,
.ui-button:hover,
.ui-button:active {
  text-decoration: none;
}

.ui-button-icon-only {
  width: 2em;
  box-sizing: border-box;
  text-indent: -9999px;
  white-space: nowrap;
}

input.ui-button.ui-button-icon-only {
  text-indent: 0;
}

.ui-button-icon-only .ui-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -8px;
  margin-left: -8px;
}

.ui-button.ui-icon-notext .ui-icon {
  padding: 0;
  width: 2.1em;
  height: 2.1em;
  text-indent: -9999px;
  white-space: nowrap;
}

input.ui-button.ui-icon-notext .ui-icon {
  width: auto;
  height: auto;
  text-indent: 0;
  white-space: normal;
  padding: 0.4em 1em;
}

input.ui-button::-moz-focus-inner,
button.ui-button::-moz-focus-inner {
  border: 0;
  padding: 0;
}

.ui-controlgroup {
  vertical-align: middle;
  display: inline-block;
}

.ui-controlgroup>.ui-controlgroup-item {
  float: left;
  margin-left: 0;
  margin-right: 0;
}

.ui-controlgroup>.ui-controlgroup-item:focus,
.ui-controlgroup>.ui-controlgroup-item.ui-visual-focus {
  z-index: 9999;
}

.ui-controlgroup-vertical>.ui-controlgroup-item {
  display: block;
  float: none;
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
  text-align: left;
}

.ui-controlgroup-vertical .ui-controlgroup-item {
  box-sizing: border-box;
}

.ui-controlgroup .ui-controlgroup-label {
  padding: 0.4em 1em;
}

.ui-controlgroup .ui-controlgroup-label span {
  font-size: 80%;
}

.ui-controlgroup-horizontal .ui-controlgroup-label+.ui-controlgroup-item {
  border-left: none;
}

.ui-controlgroup-vertical .ui-controlgroup-label+.ui-controlgroup-item {
  border-top: none;
}

.ui-controlgroup-horizontal .ui-controlgroup-label.ui-widget-content {
  border-right: none;
}

.ui-controlgroup-vertical .ui-controlgroup-label.ui-widget-content {
  border-bottom: none;
}

.ui-controlgroup-vertical .ui-spinner-input {
  width: 75%;
  width: calc(100% - 2.4em);
}

.ui-controlgroup-vertical .ui-spinner .ui-spinner-up {
  border-top-style: solid;
}

.ui-checkboxradio-label .ui-icon-background {
  box-shadow: inset 1px 1px 1px #ccc;
  border-radius: 0.12em;
  border: none;
}

.ui-checkboxradio-radio-label .ui-icon-background {
  width: 16px;
  height: 16px;
  border-radius: 1em;
  overflow: visible;
  border: none;
}

.ui-checkboxradio-radio-label.ui-checkboxradio-checked .ui-icon,
.ui-checkboxradio-radio-label.ui-checkboxradio-checked:hover .ui-icon {
  background-image: none;
  width: 8px;
  height: 8px;
  border-width: 4px;
  border-style: solid;
}

.ui-checkboxradio-disabled {
  pointer-events: none;
}

.ui-datepicker {
  width: 17em;
  padding: 0.2em 0.2em 0;
  display: none;
}

.ui-datepicker .ui-datepicker-header {
  position: relative;
  padding: 0.2em 0;
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  position: absolute;
  top: 2px;
  width: 1.8em;
  height: 1.8em;
}

.ui-datepicker .ui-datepicker-prev-hover,
.ui-datepicker .ui-datepicker-next-hover {
  top: 1px;
}

.ui-datepicker .ui-datepicker-prev {
  left: 2px;
}

.ui-datepicker .ui-datepicker-next {
  right: 2px;
}

.ui-datepicker .ui-datepicker-prev-hover {
  left: 1px;
}

.ui-datepicker .ui-datepicker-next-hover {
  right: 1px;
}

.ui-datepicker .ui-datepicker-prev span,
.ui-datepicker .ui-datepicker-next span {
  display: block;
  position: absolute;
  left: 50%;
  margin-left: -8px;
  top: 50%;
  margin-top: -8px;
}

.ui-datepicker .ui-datepicker-title {
  margin: 0 2.3em;
  line-height: 1.8em;
  text-align: center;
}

.ui-datepicker .ui-datepicker-title select {
  font-size: 1em;
  margin: 1px 0;
}

.ui-datepicker select.ui-datepicker-month,
.ui-datepicker select.ui-datepicker-year {
  width: 45%;
}

.ui-datepicker table {
  width: 100%;
  font-size: 0.9em;
  border-collapse: collapse;
  margin: 0 0 0.4em;
}

.ui-datepicker th {
  padding: 0.7em 0.3em;
  text-align: center;
  font-weight: normal;
  border: 0;
}

.ui-datepicker td {
  border: 1px solid rgba(0, 0, 0, 0);
  padding: 1px;
}

.ui-datepicker td span,
.ui-datepicker td a {
  display: block;
  padding: 0.2em;
  text-align: right;
  text-decoration: none;
}

.ui-datepicker-jday {
  text-align: end;
  width: 100%;
  font-size: smaller;
  font-weight: normal;
  color: var(--colorWhite);
}

.ui-datepicker .ui-datepicker-buttonpane {
  background-image: none;
  margin: 0.7em 0 0 0;
  padding: 0 0.2em;
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
}

.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
  float: left;
}

.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-pause {
  float: none;
}

.ui-datepicker.ui-datepicker-multi {
  width: auto;
}

.ui-datepicker-multi .ui-datepicker-group {
  float: left;
}

.ui-datepicker-multi .ui-datepicker-group table {
  width: 95%;
  margin: 0 auto 0.4em;
}

.ui-datepicker-multi-2 .ui-datepicker-group {
  width: 50%;
}

.ui-datepicker-multi-3 .ui-datepicker-group {
  width: 33.3%;
}

.ui-datepicker-multi-4 .ui-datepicker-group {
  width: 25%;
}

.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
  border-left-width: 0;
}

.ui-datepicker-multi .ui-datepicker-buttonpane {
  clear: left;
}

.ui-datepicker-row-break {
  clear: both;
  width: 100%;
  font-size: 0;
}

.ui-datepicker-rtl {
  direction: rtl;
}

.ui-datepicker-rtl .ui-datepicker-prev {
  right: 2px;
  left: auto;
}

.ui-datepicker-rtl .ui-datepicker-next {
  left: 2px;
  right: auto;
}

.ui-datepicker-rtl .ui-datepicker-prev:hover {
  right: 1px;
  left: auto;
}

.ui-datepicker-rtl .ui-datepicker-next:hover {
  left: 1px;
  right: auto;
}

.ui-datepicker-rtl .ui-datepicker-buttonpane {
  clear: right;
}

.ui-datepicker-rtl .ui-datepicker-buttonpane button {
  float: left;
}

.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current,
.ui-datepicker-rtl .ui-datepicker-group {
  float: right;
}

.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header,
.ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
  border-right-width: 0;
  border-left-width: 1px;
}

.ui-datepicker .ui-icon {
  display: block;
  text-indent: -99999px;
  overflow: hidden;
  background-repeat: no-repeat;
  left: 0.5em;
  top: 0.3em;
}

.ui-dialog {
  position: absolute;
  top: 0;
  left: 0;
  padding: 0.2em;
  outline: 0;
}

.ui-dialog .ui-dialog-titlebar {
  padding: 0.4em 1em;
  position: relative;
}

.ui-dialog .ui-dialog-title {
  float: left;
  margin: 0.1em 0;
  white-space: nowrap;
  width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ui-dialog .ui-dialog-titlebar-close {
  position: absolute;
  right: 0.3em;
  top: 50%;
  width: 20px;
  margin: -10px 0 0 0;
  padding: 1px;
  height: 20px;
}

.ui-dialog .ui-dialog-content {
  position: relative;
  border: 0;
  padding: 0.5em 1em;
  background: none;
  overflow: auto;
}

.ui-dialog .ui-dialog-buttonpane {
  text-align: left;
  border-width: 1px 0 0 0;
  background-image: none;
  margin-top: 0.5em;
  padding: 0.3em 1em 0.5em 0.4em;
}

.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
  float: right;
}

.ui-dialog .ui-dialog-buttonpane button {
  margin: 0.5em 0.4em 0.5em 0;
  cursor: pointer;
}

.ui-dialog .ui-resizable-n {
  height: 2px;
  top: 0;
}

.ui-dialog .ui-resizable-e {
  width: 2px;
  right: 0;
}

.ui-dialog .ui-resizable-s {
  height: 2px;
  bottom: 0;
}

.ui-dialog .ui-resizable-w {
  width: 2px;
  left: 0;
}

.ui-dialog .ui-resizable-se,
.ui-dialog .ui-resizable-sw,
.ui-dialog .ui-resizable-ne,
.ui-dialog .ui-resizable-nw {
  width: 7px;
  height: 7px;
}

.ui-dialog .ui-resizable-se {
  right: 0;
  bottom: 0;
}

.ui-dialog .ui-resizable-sw {
  left: 0;
  bottom: 0;
}

.ui-dialog .ui-resizable-ne {
  right: 0;
  top: 0;
}

.ui-dialog .ui-resizable-nw {
  left: 0;
  top: 0;
}

.ui-draggable .ui-dialog-titlebar {
  cursor: move;
}

.ui-draggable-handle {
  -ms-touch-action: none;
  touch-action: none;
}

.ui-resizable {
  position: relative;
}

.ui-resizable-handle {
  position: absolute;
  font-size: 0.1px;
  display: block;
  -ms-touch-action: none;
  touch-action: none;
}

.ui-resizable-disabled .ui-resizable-handle,
.ui-resizable-autohide .ui-resizable-handle {
  display: none;
}

.ui-resizable-n {
  cursor: n-resize;
  height: 7px;
  width: 100%;
  top: -5px;
  left: 0;
}

.ui-resizable-s {
  cursor: s-resize;
  height: 7px;
  width: 100%;
  bottom: -5px;
  left: 0;
}

.ui-resizable-e {
  cursor: e-resize;
  width: 7px;
  right: -5px;
  top: 0;
  height: 100%;
}

.ui-resizable-w {
  cursor: w-resize;
  width: 7px;
  left: -5px;
  top: 0;
  height: 100%;
}

.ui-resizable-se {
  cursor: se-resize;
  width: 12px;
  height: 12px;
  right: 1px;
  bottom: 1px;
}

.ui-resizable-sw {
  cursor: sw-resize;
  width: 9px;
  height: 9px;
  left: -5px;
  bottom: -5px;
}

.ui-resizable-nw {
  cursor: nw-resize;
  width: 9px;
  height: 9px;
  left: -5px;
  top: -5px;
}

.ui-resizable-ne {
  cursor: ne-resize;
  width: 9px;
  height: 9px;
  right: -5px;
  top: -5px;
}

.ui-progressbar {
  height: 2em;
  text-align: left;
  overflow: hidden;
}

.ui-progressbar .ui-progressbar-value {
  margin: -1px;
  height: 100%;
}

.ui-progressbar .ui-progressbar-overlay {
  background: url('data:image/gif;base64,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');
  height: 100%;
  filter: alpha(opacity=25);
  opacity: 0.25;
}

.ui-progressbar-indeterminate .ui-progressbar-value {
  background-image: none;
}

.ui-selectable {
  -ms-touch-action: none;
  touch-action: none;
}

.ui-selectable-helper {
  position: absolute;
  z-index: 100;
  border: 1px dotted black;
}

.ui-selectmenu-menu {
  padding: 0;
  margin: 0;
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}

.ui-selectmenu-menu .ui-menu {
  overflow: auto;
  overflow-x: hidden;
  padding-bottom: 1px;
}

.ui-selectmenu-menu .ui-menu .ui-selectmenu-optgroup {
  font-size: 1em;
  font-weight: bold;
  line-height: 1.5;
  padding: 2px 0.4em;
  margin: 0.5em 0 0 0;
  height: auto;
  border: 0;
}

.ui-selectmenu-open {
  display: block;
}

.ui-selectmenu-text {
  display: block;
  margin-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ui-selectmenu-button.ui-button {
  text-align: left;
  white-space: nowrap;
  width: 14em;
}

.ui-selectmenu-icon.ui-icon {
  float: right;
  margin-top: 0;
}

.ui-slider {
  position: relative;
  text-align: left;
  width: 160px;
  margin-bottom: 10px;
}

.ui-slider .ui-slider-handle {
  background-color: var(--color-dark-background);
  border: 2px solid var(--color-primary);
  width: 16px;
  /* Larger circle size */
  height: 16px;
  border-radius: 50%;
  position: absolute;
  top: -5px;
  /* Centers the handle vertically on the track */
  transform: translateX(-50%);
  /* Centers the handle horizontally on the track */
  cursor: pointer;
}

.ui-slider .ui-slider-range {
  background-color: #1a2433;
  /* Dark background */
  border-radius: 10px;
  /* Rounded corners */
  height: 8px;
  /* Slim slider track */
  position: absolute;
  z-index: 1;
  font-size: 0.7em;
  display: block;
  border: 0;
  background-position: 0 0;
}

.ui-slider.ui-state-disabled .ui-slider-handle,
.ui-slider.ui-state-disabled .ui-slider-range {
  filter: inherit;
}

.ui-slider-horizontal {
  height: 0px;
  border: 2px solid var(--color-primary) !important
}

.ui-slider-horizontal .ui-slider-handle {
  top: -7px;
}

.ui-slider-horizontal .ui-slider-range {
  top: 0;
  height: 100%;
}

.ui-slider-horizontal .ui-slider-range-min {
  left: 0;
}

.ui-slider-horizontal .ui-slider-range-max {
  right: 0;
}

.ui-slider-vertical {
  width: 0.8em;
  height: 100px;
}

.ui-slider-vertical .ui-slider-handle {
  left: -0.3em;
  margin-left: 0;
  margin-bottom: -0.6em;
}

.ui-slider-vertical .ui-slider-range {
  left: 0;
  width: 100%;
}

.ui-slider-vertical .ui-slider-range-min {
  bottom: 0;
}

.ui-slider-vertical .ui-slider-range-max {
  top: 0;
}

.ui-sortable-handle {
  -ms-touch-action: none;
  touch-action: none;
}

.ui-spinner {
  position: relative;
  display: inline-block;
  overflow: hidden;
  padding: 0;
  vertical-align: middle;
}

.ui-spinner-input {
  border: none;
  background: none;
  color: inherit;
  padding: 0.222em 0;
  margin: 0.2em 0;
  vertical-align: middle;
  margin-left: 0.4em;
  margin-right: 2em;
}

.ui-spinner-button {
  width: 1.6em;
  height: 50%;
  font-size: 0.5em;
  padding: 0;
  margin: 0;
  text-align: center;
  position: absolute;
  cursor: default;
  display: block;
  overflow: hidden;
  right: 0;
}

.ui-spinner a.ui-spinner-button {
  border-top-style: none;
  border-bottom-style: none;
  border-right-style: none;
}

.ui-spinner-up {
  top: 0;
}

.ui-spinner-down {
  bottom: 0;
}

.ui-tabs {
  position: relative;
  padding: 0.2em;
}

.ui-tabs .ui-tabs-nav {
  margin: 0;
  padding: 0.2em 0.2em 0;
}

.ui-tabs .ui-tabs-nav li {
  list-style: none;
  float: left;
  position: relative;
  top: 0;
  margin: 1px 0.2em 0 0;
  border-bottom-width: 0;
  padding: 0;
  white-space: nowrap;
}

.ui-tabs .ui-tabs-nav .ui-tabs-anchor {
  float: left;
  padding: 0.5em 1em;
  text-decoration: none;
}

.ui-tabs .ui-tabs-nav li.ui-tabs-active {
  margin-bottom: -1px;
  padding-bottom: 1px;
}

.ui-tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor,
.ui-tabs .ui-tabs-nav li.ui-state-disabled .ui-tabs-anchor,
.ui-tabs .ui-tabs-nav li.ui-tabs-loading .ui-tabs-anchor {
  cursor: text;
}

.ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor {
  cursor: pointer;
}

.ui-tabs .ui-tabs-panel {
  display: block;
  border-width: 0;
  padding: 1em 1.4em;
  background: none;
}

.ui-tooltip {
  padding: 8px;
  position: absolute;
  z-index: 9999;
  max-width: 300px;
}

body .ui-tooltip {
  border-width: 2px;
}

.ui-widget {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1em;
}

.ui-widget .ui-widget {
  font-size: 1em;
}

.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1em;
}

.ui-widget-content a {
  color: #333;
}

.ui-widget-header a {
  color: #333;
}

.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited,
a.ui-button,
a:link.ui-button,
a:visited.ui-button,
.ui-button {
  color: #454545;
  text-decoration: none;
}

.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
  border: 1px solid #ccc;
  background: rgba(237, 237, 237, 0.6);
  font-weight: normal;
  color: #2b2b2b;
}

.ui-state-hover a,
.ui-state-hover a:hover,
.ui-state-hover a:link,
.ui-state-hover a:visited,
.ui-state-focus a,
.ui-state-focus a:hover,
.ui-state-focus a:link,
.ui-state-focus a:visited,
a.ui-button:hover,
a.ui-button:focus {
  color: #2b2b2b;
  text-decoration: none;
}

.ui-visual-focus {
  box-shadow: 0 0 3px 1px rgb(94, 158, 214);
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
  border: 1px solid #ff9800 !important; /* 橙色边框 */
  background: rgba(255, 152, 0, 0.8) !important; /* 橙色背景 */
  border-radius: 3px;
}

.ui-icon-background,
.ui-state-active .ui-icon-background {
  border: #003eff;
  background-color: #fff;
}

.ui-state-active a,
.ui-state-active a:link,
.ui-state-active a:visited {
  color: #fff;
  text-decoration: none;
}

.ui-state-checked {
  border: 1px solid #dad55e;
  background: #fffa90;
}

.ui-state-highlight a,
.ui-widget-content .ui-state-highlight a,
.ui-widget-header .ui-state-highlight a {
  color: #777620;
}

.ui-state-error,
.ui-widget-content .ui-state-error,
.ui-widget-header .ui-state-error {
  border: 1px solid #f1a899;
  background: #fddfdf;
  color: #5f3f3f;
}

.ui-state-error a,
.ui-widget-content .ui-state-error a,
.ui-widget-header .ui-state-error a {
  color: #5f3f3f;
}

.ui-state-error-text,
.ui-widget-content .ui-state-error-text,
.ui-widget-header .ui-state-error-text {
  color: #5f3f3f;
}

.ui-priority-primary,
.ui-widget-content .ui-priority-primary,
.ui-widget-header .ui-priority-primary {
  font-weight: bold;
}

.ui-priority-secondary,
.ui-widget-content .ui-priority-secondary,
.ui-widget-header .ui-priority-secondary {
  opacity: 0.7;
  filter: Alpha(Opacity=70);
  font-weight: normal;
}

.ui-state-disabled,
.ui-widget-content .ui-state-disabled,
.ui-widget-header .ui-state-disabled {
  opacity: 0.35;
  filter: Alpha(Opacity=35);
  background-image: none;
}

.ui-state-disabled .ui-icon {
  filter: Alpha(Opacity=35);
}

.ui-icon,
.ui-widget-content .ui-icon {
  background-image: url('images/ui-icons_444444_256x240.png');
}

.ui-widget-header .ui-icon {
  background-image: url('images/ui-icons_444444_256x240.png');
}

.ui-state-hover .ui-icon,
.ui-state-focus .ui-icon,
.ui-button:hover .ui-icon,
.ui-button:focus .ui-icon {
  background-image: url('images/ui-icons_555555_256x240.png');
}

.ui-state-active .ui-icon,
.ui-button:active .ui-icon {
  background-image: url('images/ui-icons_ffffff_256x240.png');
}

.ui-state-highlight .ui-icon,
.ui-button .ui-state-highlight.ui-icon {
  background-image: url('images/ui-icons_777620_256x240.png');
}

.ui-state-error .ui-icon,
.ui-state-error-text .ui-icon {
  background-image: url('images/ui-icons_cc0000_256x240.png');
}

.ui-button .ui-icon {
  background-image: url('images/ui-icons_777777_256x240.png');
}

.ui-icon-blank {
  background-position: 16px 16px;
}

.ui-icon-caret-1-n {
  background-position: 0 0;
}

.ui-icon-caret-1-ne {
  background-position: -16px 0;
}

.ui-icon-caret-1-e {
  background-position: -32px 0;
}

.ui-icon-caret-1-se {
  background-position: -48px 0;
}

.ui-icon-caret-1-s {
  background-position: -65px 0;
}

.ui-icon-caret-1-sw {
  background-position: -80px 0;
}

.ui-icon-caret-1-w {
  background-position: -96px 0;
}

.ui-icon-caret-1-nw {
  background-position: -112px 0;
}

.ui-icon-caret-2-n-s {
  background-position: -128px 0;
}

.ui-icon-caret-2-e-w {
  background-position: -144px 0;
}

.ui-icon-triangle-1-n {
  background-position: 0 -16px;
}

.ui-icon-triangle-1-ne {
  background-position: -16px -16px;
}

.ui-icon-triangle-1-e {
  background-position: -32px -16px;
}

.ui-icon-triangle-1-se {
  background-position: -48px -16px;
}

.ui-icon-triangle-1-s {
  background-position: -65px -16px;
}

.ui-icon-triangle-1-sw {
  background-position: -80px -16px;
}

.ui-icon-triangle-1-w {
  background-position: -96px -16px;
}

.ui-icon-triangle-1-nw {
  background-position: -112px -16px;
}

.ui-icon-triangle-2-n-s {
  background-position: -128px -16px;
}

.ui-icon-triangle-2-e-w {
  background-position: -144px -16px;
}

.ui-icon-arrow-1-n {
  background-position: 0 -32px;
}

.ui-icon-arrow-1-ne {
  background-position: -16px -32px;
}

.ui-icon-arrow-1-e {
  background-position: -32px -32px;
}

.ui-icon-arrow-1-se {
  background-position: -48px -32px;
}

.ui-icon-arrow-1-s {
  background-position: -65px -32px;
}

.ui-icon-arrow-1-sw {
  background-position: -80px -32px;
}

.ui-icon-arrow-1-w {
  background-position: -96px -32px;
}

.ui-icon-arrow-1-nw {
  background-position: -112px -32px;
}

.ui-icon-arrow-2-n-s {
  background-position: -128px -32px;
}

.ui-icon-arrow-2-ne-sw {
  background-position: -144px -32px;
}

.ui-icon-arrow-2-e-w {
  background-position: -160px -32px;
}

.ui-icon-arrow-2-se-nw {
  background-position: -176px -32px;
}

.ui-icon-arrowstop-1-n {
  background-position: -192px -32px;
}

.ui-icon-arrowstop-1-e {
  background-position: -208px -32px;
}

.ui-icon-arrowstop-1-s {
  background-position: -224px -32px;
}

.ui-icon-arrowstop-1-w {
  background-position: -240px -32px;
}

.ui-icon-arrowthick-1-n {
  background-position: 1px -48px;
}

.ui-icon-arrowthick-1-ne {
  background-position: -16px -48px;
}

.ui-icon-arrowthick-1-e {
  background-position: -32px -48px;
}

.ui-icon-arrowthick-1-se {
  background-position: -48px -48px;
}

.ui-icon-arrowthick-1-s {
  background-position: -64px -48px;
}

.ui-icon-arrowthick-1-sw {
  background-position: -80px -48px;
}

.ui-icon-arrowthick-1-w {
  background-position: -96px -48px;
}

.ui-icon-arrowthick-1-nw {
  background-position: -112px -48px;
}

.ui-icon-arrowthick-2-n-s {
  background-position: -128px -48px;
}

.ui-icon-arrowthick-2-ne-sw {
  background-position: -144px -48px;
}

.ui-icon-arrowthick-2-e-w {
  background-position: -160px -48px;
}

.ui-icon-arrowthick-2-se-nw {
  background-position: -176px -48px;
}

.ui-icon-arrowthickstop-1-n {
  background-position: -192px -48px;
}

.ui-icon-arrowthickstop-1-e {
  background-position: -208px -48px;
}

.ui-icon-arrowthickstop-1-s {
  background-position: -224px -48px;
}

.ui-icon-arrowthickstop-1-w {
  background-position: -240px -48px;
}

.ui-icon-arrowreturnthick-1-w {
  background-position: 0 -64px;
}

.ui-icon-arrowreturnthick-1-n {
  background-position: -16px -64px;
}

.ui-icon-arrowreturnthick-1-e {
  background-position: -32px -64px;
}

.ui-icon-arrowreturnthick-1-s {
  background-position: -48px -64px;
}

.ui-icon-arrowreturn-1-w {
  background-position: -64px -64px;
}

.ui-icon-arrowreturn-1-n {
  background-position: -80px -64px;
}

.ui-icon-arrowreturn-1-e {
  background-position: -96px -64px;
}

.ui-icon-arrowreturn-1-s {
  background-position: -112px -64px;
}

.ui-icon-arrowrefresh-1-w {
  background-position: -128px -64px;
}

.ui-icon-arrowrefresh-1-n {
  background-position: -144px -64px;
}

.ui-icon-arrowrefresh-1-e {
  background-position: -160px -64px;
}

.ui-icon-arrowrefresh-1-s {
  background-position: -176px -64px;
}

.ui-icon-arrow-4 {
  background-position: 0 -80px;
}

.ui-icon-arrow-4-diag {
  background-position: -16px -80px;
}

.ui-icon-extlink {
  background-position: -32px -80px;
}

.ui-icon-newwin {
  background-position: -48px -80px;
}

.ui-icon-refresh {
  background-position: -64px -80px;
}

.ui-icon-shuffle {
  background-position: -80px -80px;
}

.ui-icon-transfer-e-w {
  background-position: -96px -80px;
}

.ui-icon-transferthick-e-w {
  background-position: -112px -80px;
}

.ui-icon-folder-collapsed {
  background-position: 0 -96px;
}

.ui-icon-folder-open {
  background-position: -16px -96px;
}

.ui-icon-document {
  background-position: -32px -96px;
}

.ui-icon-document-b {
  background-position: -48px -96px;
}

.ui-icon-note {
  background-position: -64px -96px;
}

.ui-icon-mail-closed {
  background-position: -80px -96px;
}

.ui-icon-mail-open {
  background-position: -96px -96px;
}

.ui-icon-suitcase {
  background-position: -112px -96px;
}

.ui-icon-comment {
  background-position: -128px -96px;
}

.ui-icon-person {
  background-position: -144px -96px;
}

.ui-icon-print {
  background-position: -160px -96px;
}

.ui-icon-trash {
  background-position: -176px -96px;
}

.ui-icon-locked {
  background-position: -192px -96px;
}

.ui-icon-unlocked {
  background-position: -208px -96px;
}

.ui-icon-bookmark {
  background-position: -224px -96px;
}

.ui-icon-tag {
  background-position: -240px -96px;
}

.ui-icon-home {
  background-position: 0 -112px;
}

.ui-icon-flag {
  background-position: -16px -112px;
}

.ui-icon-calendar {
  background-position: -32px -112px;
}

.ui-icon-cart {
  background-position: -48px -112px;
}

.ui-icon-pencil {
  background-position: -64px -112px;
}

.ui-icon-clock {
  background-position: -80px -112px;
}

.ui-icon-disk {
  background-position: -96px -112px;
}

.ui-icon-calculator {
  background-position: -112px -112px;
}

.ui-icon-zoomin {
  background-position: -128px -112px;
}

.ui-icon-zoomout {
  background-position: -144px -112px;
}

.ui-icon-search {
  background-position: -160px -112px;
}

.ui-icon-wrench {
  background-position: -176px -112px;
}

.ui-icon-gear {
  background-position: -192px -112px;
}

.ui-icon-heart {
  background-position: -208px -112px;
}

.ui-icon-star {
  background-position: -224px -112px;
}

.ui-icon-link {
  background-position: -240px -112px;
}

.ui-icon-cancel {
  background-position: 0 -128px;
}

.ui-icon-plus {
  background-position: -16px -128px;
}

.ui-icon-plusthick {
  background-position: -32px -128px;
}

.ui-icon-minus {
  background-position: -48px -128px;
}

.ui-icon-minusthick {
  background-position: -64px -128px;
}

.ui-icon-close {
  background-position: -80px -128px;
}

.ui-icon-closethick {
  background-position: -96px -128px;
}

.ui-icon-key {
  background-position: -112px -128px;
}

.ui-icon-lightbulb {
  background-position: -128px -128px;
}

.ui-icon-scissors {
  background-position: -144px -128px;
}

.ui-icon-clipboard {
  background-position: -160px -128px;
}

.ui-icon-copy {
  background-position: -176px -128px;
}

.ui-icon-contact {
  background-position: -192px -128px;
}

.ui-icon-image {
  background-position: -208px -128px;
}

.ui-icon-video {
  background-position: -224px -128px;
}

.ui-icon-script {
  background-position: -240px -128px;
}

.ui-icon-alert {
  background-position: 0 -144px;
}

.ui-icon-info {
  background-position: -16px -144px;
}

.ui-icon-notice {
  background-position: -32px -144px;
}

.ui-icon-help {
  background-position: -48px -144px;
}

.ui-icon-check {
  background-position: -64px -144px;
}

.ui-icon-bullet {
  background-position: -80px -144px;
}

.ui-icon-radio-on {
  background-position: -96px -144px;
}

.ui-icon-radio-off {
  background-position: -112px -144px;
}

.ui-icon-pin-w {
  background-position: -128px -144px;
}

.ui-icon-pin-s {
  background-position: -144px -144px;
}

.ui-icon-play {
  background-position: 0 -160px;
}

.ui-icon-pause {
  background-position: -16px -160px;
}

.ui-icon-seek-next {
  background-position: -32px -160px;
}

.ui-icon-seek-prev {
  background-position: -48px -160px;
}

.ui-icon-seek-end {
  background-position: -64px -160px;
}

.ui-icon-seek-start {
  background-position: -80px -160px;
}

.ui-icon-seek-first {
  background-position: -80px -160px;
}

.ui-icon-stop {
  background-position: -96px -160px;
}

.ui-icon-eject {
  background-position: -112px -160px;
}

.ui-icon-volume-off {
  background-position: -128px -160px;
}

.ui-icon-volume-on {
  background-position: -144px -160px;
}

.ui-icon-power {
  background-position: 0 -176px;
}

.ui-icon-signal-diag {
  background-position: -16px -176px;
}

.ui-icon-signal {
  background-position: -32px -176px;
}

.ui-icon-battery-0 {
  background-position: -48px -176px;
}

.ui-icon-battery-1 {
  background-position: -64px -176px;
}

.ui-icon-battery-2 {
  background-position: -80px -176px;
}

.ui-icon-battery-3 {
  background-position: -96px -176px;
}

.ui-icon-circle-plus {
  background-position: 0 -192px;
}

.ui-icon-circle-minus {
  background-position: -16px -192px;
}

.ui-icon-circle-close {
  background-position: -32px -192px;
}

.ui-icon-circle-triangle-s {
  background-position: -64px -192px;
}

.ui-icon-circle-triangle-n {
  background-position: -96px -192px;
}

.ui-icon-circle-arrow-e {
  background-position: -112px -192px;
}

.ui-icon-circle-arrow-s {
  background-position: -128px -192px;
}

.ui-icon-circle-arrow-w {
  background-position: -144px -192px;
}

.ui-icon-circle-arrow-n {
  background-position: -160px -192px;
}

.ui-icon-circle-zoomin {
  background-position: -176px -192px;
}

.ui-icon-circle-zoomout {
  background-position: -192px -192px;
}

.ui-icon-circle-check {
  background-position: -208px -192px;
}

.ui-icon-circlesmall-plus {
  background-position: 0 -208px;
}

.ui-icon-circlesmall-minus {
  background-position: -16px -208px;
}

.ui-icon-circlesmall-close {
  background-position: -32px -208px;
}

.ui-icon-squaresmall-plus {
  background-position: -48px -208px;
}

.ui-icon-squaresmall-minus {
  background-position: -64px -208px;
}

.ui-icon-squaresmall-close {
  background-position: -80px -208px;
}

.ui-icon-grip-dotted-vertical {
  background-position: 0 -224px;
}

.ui-icon-grip-dotted-horizontal {
  background-position: -16px -224px;
}

.ui-icon-grip-solid-vertical {
  background-position: -32px -224px;
}

.ui-icon-grip-solid-horizontal {
  background-position: -48px -224px;
}

.ui-icon-gripsmall-diagonal-se {
  background-position: -64px -224px;
}

.ui-icon-grip-diagonal-se {
  background-position: -80px -224px;
}

.ui-corner-all,
.ui-corner-top,
.ui-corner-left,
.ui-corner-tl {
  border-top-left-radius: 3px;
}

.ui-corner-all,
.ui-corner-top,
.ui-corner-right,
.ui-corner-tr {
  border-top-right-radius: 3px;
}

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-left,
.ui-corner-bl {
  border-bottom-left-radius: 3px;
}

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br {
  border-bottom-right-radius: 3px;
}

.ui-widget-shadow {
  -webkit-box-shadow: 0 0 5px #666;
  box-shadow: 0 0 5px #666;
}

/* *********** End Calendar CSS *********** */

#colorPick * {
  -webkit-transition: all linear 0.2s;
  -moz-transition: all linear 0.2s;
  -ms-transition: all linear 0.2s;
  -o-transition: all linear 0.2s;
  transition: all linear 0.2s;
}

#colorPick {
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.5);
  padding: 15px;
  font-family: 'Open Sans', sans-serif;
  width: fit-content;
  background: var(--color-dark-background);
  border: 5px solid var(--color-dark-border);
  /* Grid of 4 divs wide */
  display: grid !important;
  grid-template-columns: repeat(6, 1fr);
  grid-row-gap: 15px;
}

#colorPick span {
  font-size: 9pt;
  text-transform: uppercase;
  font-weight: bold;
  color: #bbb;
  margin-bottom: 5px;
  display: block;
  clear: both;
}

.customColorHash {
  border-radius: 5px;
  height: 23px;
  width: 122px;
  margin: 1px 4px;
  padding: 0 4px;
  border: 1px solid #babbba;
  outline: none;
}

.customColorHash.error {
  border-color: #ff424c;
  color: #ff424c;
}

.colorPickButton {
  border-radius: 5px;
  width: 20px;
  height: 20px;
  margin: 0px 3px;
  cursor: pointer;
  display: inline-block;
  border: thin solid #eee;
}

.colorPickButton:hover {
  transform: scale(1.1);
}

.colorPickDummy {
  background: #fff;
  border: 1px dashed #bbb;
}

@keyframes shake {
  0% {
    filter: brightness(1.5);
  }

  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }

  100% {
    filter: '';
  }
}

.shake {
  animation: shake 1s;
  animation-iteration-count: infinite;
}

#colorbox-div {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
  padding: 5%;
}

#colorbox-container {
  background-color: 'white';
  transform: translateX(-200%);
  transition: transform 1s ease-in-out 0s;
  display: block;
  z-index: 100;
  width: 95%;
  height: 95%;
  margin: auto;
  border-color: var(--color-dark-border);
  border-width: 10px;
  border-style: solid;
  border-radius: 5px;
}

#colorbox-iframe,
#colorbox-img {
  width: 100%;
  height: calc(100% - 34px);
  border: none;
}

/****************************************************
 * Tooltip Styles
 ****************************************************/

/* Add this attribute to the element that needs a tooltip */
[data-tooltip] {
  position: relative;
  z-index: 2;
  cursor: pointer;
}

/* Hide the tooltip content by default */
[data-tooltip]:before,
[data-tooltip]:after {
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}

/* Position tooltip above all other elements */
[data-tooltip]:before {
  z-index: 9999;
  position: absolute;
  display: block;
  width: calc(150px / var(--system-scale-factor, 1));
  margin-left: calc(-75px / var(--system-scale-factor, 1));
  /* Use half of the width to center the tooltip */
  overflow: visible;

  background-color: var(--color-dark-background);
  text-align: center;
  padding: calc(8px / var(--system-scale-factor, 1));
  border-width: calc(3px / var(--system-scale-factor, 1));
  border-color: var(--color-dark-border);
  border-style: solid;
  color: #ffffff;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
  content: attr(data-tooltip);
}

[data-tooltip][data-position='bottom']:before {
  bottom: -100%;
  left: 50%;
}

[data-tooltip][data-position='top']:before {
  bottom: 125%;
  top: auto;
  left: 50%;
}

[data-tooltip][data-position='left']:before {
  right: 100%;
  left: auto;
}

[data-tooltip][data-position='right']:before {
  left: 100%;
  right: auto;
}

/* Show tooltip content on hover */
[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
  visibility: visible;
  opacity: 1;
}

/* Large Cellphones or Smaller */
.material-tooltip {
  display: none !important;
  background-color: #323232;
}



#menu-launches,
#menu-record,
#menu-color-scheme,
#social,
#fastCompSettings {
  display: none;
}

#footer-toggle-wrapper {
  width: 100%;
  z-index: 1;
  position: relative;
}

.footer-slide-down {
  bottom: calc(-145px / var(--system-scale-factor, 1)) !important;
  /* 🔥 移除固定高度，允许拖动调整 */
  top: auto !important;
}

.footer-slide-trans {
  transition: 1s;
}

.footer-slide-up {
  bottom: 0px !important;
  top: auto !important;
}

.side-menu {
  position: relative;
  height: 100%;
  border: none !important; /* 移除所有边框 */
  background: transparent !important; /* 强制透明背景 */
  background-color: transparent !important;
  /* 🔥 不要对所有子元素强制移除背景图片，这会影响国旗显示 */
  color: white;
  width: 100%;
  top: 0px;
  bottom: 0px;
  overflow: auto;
  z-index: 10;
  padding: 0px 5px;
}

/* 🔥 只对需要透明的元素设置背景图片为none，排除国旗 */
.side-menu > *:not(.fi):not([class*="fi-"]):not(#sat-infobox-fi) {
  background-image: none !important;
}

#sensor-timeline-menu-content,
#satellite-timeline-menu-content {
  padding: 0px !important;
}

.side-menu-settings {
  position: relative;
  height: 100%;
  border: none !important; /* 移除所有边框 */
  background: transparent !important; /* 强制透明背景 */
  background-color: transparent !important;
  background-image: none !important;
  color: white;
  width: 100%;
  top: 0px;
  bottom: 0px;
  overflow: auto;
  z-index: 3;
  padding: 0px 10px;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 10px;
}

/* 🔥 确保设置菜单在父容器隐藏时也隐藏 */
.side-menu-parent.start-hidden .side-menu-settings {
  display: none !important;
}

/* Only Small Cellphones */
#search {
  height: calc(36px / var(--system-scale-factor, 1));
  width: 100%;
  padding: 0 calc(30px / var(--system-scale-factor, 1)) 0 calc(12px / var(--system-scale-factor, 1));
  margin: calc(3px / var(--system-scale-factor, 1)) 0;
  border: none !important; /* 强制移除所有边框 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important; /* 只保留底部细线 */
  border-radius: 0 !important;
  background: transparent !important;
  font-size: calc(16px / var(--system-scale-factor, 1)) !important;
  color: #fff;
  outline: none !important;
  box-shadow: none !important;
  box-sizing: border-box;
  position: relative;
  line-height: calc(20px / var(--system-scale-factor, 1)) !important;
}

#search:focus {
  border-color: rgba(255, 255, 255, 0.2);
}

#search-clear {
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #fff;
  font-size: 16px;
  line-height: 1;
  z-index: 2;
  padding: 2px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0 4px 4px 0;
  width: 24px;
  height: calc(100% - 4px);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
}

#search-clear:hover {
  color: white;
}

/* Removed duplicate background rule */
#search-clear::before {
  content: "x";
  font-size: calc(20px / var(--system-scale-factor, 1)) !important;
  color: white;
}

#search-holder {
  position: relative;
  display: flex;
  width: 300px;
  margin-right: 0;
  height: 100%;
  align-items: center;
}

#search-holder .search-container {
  position: relative;
  width: 100%;
}

#search-holder .search-input-wrapper {
  position: relative;
  width: 100%;
}

#search-holder input {
  width: 100%;
  padding-right: 25px;
  box-sizing: border-box;
}

#search-holder input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px var(--color-dark-background) inset !important;
}

/* Removed duplicate rule */

.search-slide-down {
  width: 160px;
  transition: 1s;
}

.search-slide-down input[type='search'] {
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
  text-decoration: none;
}

.search-slide-up {
  transition: 1s;
  width: 0px;
}

.search-slide-up input[type='search'] {
  transition: 0.6s;
  width: 0px;
  padding-left: 0px;
}

#nav-mobile2 li {
  line-height: normal;
  height: 100%;
}

.top-menu-icons {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  margin: 0 5px;
  vertical-align: middle;
}

.top-menu-icons {
  padding: 0px;
  height: 100%;
  display: flex;
  align-items: center;
  transition: 1s;
}

.top-menu-icons img {
  width: 24px;
  height: 24px;
  object-fit: contain;
  margin: 3px;
}

nav {
  color: #fff;
  background-color: transparent;
  width: 100%;
  height: 50px;
  line-height: 50px;
}

#legend-hover-menu {
  display: none;
  padding: 10px;
  color: white;
  background: var(--color-dark-background);
  top: var(--top-menu-height);
  z-index: 100;
  position: fixed;
  overflow: auto;
  width: 100%;
  border-width: 0px 0px 5px 0px;
  border-style: solid;
  border-color: var(--color-dark-border);
}

#legend-hover-menu li {
  display: flex;
  align-items: center;
}

#toast-container {
  min-width: 100%;
  top: 75px;
}

nav,
nav .nav-wrapper i,
nav a.sidenav-trigger,
nav a.sidenav-trigger i {
  height: var(--nav-bar-height);
  line-height: var(--nav-bar-height);
  box-shadow: none;
  -webkit-box-shadow: none;
}

#sensor-timeline-menu,
#satellite-timeline-menu {
  display: none;
  position: fixed !important;
  width: 100vw !important;
  height: 100vh !important;
  left: 0 !important;
  top: 0 !important;
  margin: 0;
  border: none !important;
  z-index: 9999 !important;
  overflow: hidden !important;
}

/* Always */
#nextLaunch-menu {
  display: block;
}

#nextLaunch-table tbody tr td {
  padding: 5px 5px;
}

/* Cell Phone Only */
@media (max-width: 694px) {
  #nextLaunch-menu {
    width: 100%;
  }
}

/* Tablet Only */
@media (min-width: 695px) and (max-width: 1300px) {
  #nextLaunch-menu {
    width: 833px; /* 从625增加1/3到833 (625 * 4/3 ≈ 833) */
  }
}

/* Desktop Only */
@media (min-width: 1301px) {
  #nextLaunch-menu {
    width: 833px; /* 从625增加1/3到833 (625 * 4/3 ≈ 833) */
  }
}

#map-menu {
  display: none;
  position: fixed; /* 🔥 改为fixed定位 */
  width: 100vw; /* 🔥 宽度铺满整个屏幕 */
  height: 100vh; /* 🔥 高度铺满整个屏幕 */
  left: 0;
  top: 0; /* 🔥 从顶部开始 */
  margin: 0;
  border-top: 3px solid var(--color-primary);
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
  /* Workaround to keep dots aligned and antarctica white */
  background: rgba(255, 255, 255, 0.85);
  color: white;
  z-index: 10;
  padding: 0;
}

.map-look {
  width: 12px;
  height: 12px;
  border-radius: 20%;
  cursor: pointer;
}

#map-sensor {
  display: none;
  position: absolute;
  z-index: 15;
  width: 25px;
  height: 25px;
}

#map-sat {
  display: block;
  position: absolute;
  z-index: 15;
  width: 25px;
  height: 25px;
}

.map-item {
  position: absolute;
  z-index: 11;
}

/* 历史轨道参数输入区样式优化 */
#history-track-menu input,
#history-track-menu select {
  text-align: center !important;
  line-height: 32px !important;
  height: 32px !important;
  padding: 0 8px !important;
  box-sizing: border-box !important;
  display: inline-block !important;
  vertical-align: baseline !important;
}

#history-track-menu input[type="date"] {
  text-align: center !important;
  line-height: 32px !important;
  height: 32px !important;
  padding: 0 8px !important;
  box-sizing: border-box !important;
  display: inline-block !important;
  vertical-align: baseline !important;
}

/* 历史轨道标签样式优化 */
#history-track-menu label {
  line-height: 32px !important;
  height: 32px !important;
  display: inline-block !important;
  margin-bottom: 0 !important;
  vertical-align: baseline !important;
  font-size: 16px !important;
  color: #ffffff !important;
  white-space: nowrap !important;
}

/* 历史轨道表单容器对齐 */
#history-track-form {
  align-items: baseline !important;
}

#history-track-form > div {
  align-items: baseline !important;
}

/* 历史轨道按钮样式对齐 */
#history-track-menu .btn,
#history-track-menu button {
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 16px !important;
  box-sizing: border-box !important;
  vertical-align: baseline !important;
  display: inline-block !important;
  margin: 0 !important;
}

/* 确保获取数据按钮与其他元素对齐 */
#history-get-data {
  height: 32px !important;
  line-height: 32px !important;
  padding: 0 16px !important;
  box-sizing: border-box !important;
  vertical-align: baseline !important;
  margin: 0 0 0 8px !important;
}

/* 重置历史轨道表单元素的默认样式 */
#history-track-form * {
  margin: 0 !important;
  padding: 0 !important;
}

#history-track-form input,
#history-track-form label,
#history-track-form button {
  margin: 0 !important;
  font-family: inherit !important;
  font-size: inherit !important;
}

/* 确保日期输入框的特殊样式不影响对齐 */
#history-track-menu input[type="date"]::-webkit-calendar-picker-indicator {
  vertical-align: baseline !important;
}

/* 修复可能的浏览器兼容性问题 */
#history-track-form {
  -webkit-box-align: baseline !important;
  -ms-flex-align: baseline !important;
}

#history-track-form > div {
  -webkit-box-align: baseline !important;
  -ms-flex-align: baseline !important;
}

/* ✅ 更合理的标签样式解决方案 - 恢复Materialize行为 */
.input-field label:not(.switch label):not(.lever):not(#history-track-menu label) {
  font-size: var(--font-size-md) !important;
  color: #b3e5fc !important;
  font-weight: 400 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  pointer-events: none !important;
  z-index: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1.2 !important;
  cursor: text !important;
  transform: translateY(calc(12px / var(--system-scale-factor))) !important;
  transform-origin: 0% 100% !important;
  transition: transform 0.2s ease-out, color 0.2s ease-out !important;
}

/* 标签激活状态 - 移到输入框上方 */
.input-field label.active:not(.switch label):not(.lever):not(#history-track-menu label),
.input-field input:focus + label:not(.switch label):not(.lever):not(#history-track-menu label),
.input-field input:not(:placeholder-shown) + label:not(.switch label):not(.lever):not(#history-track-menu label),
.input-field input[type]:not(:placeholder-shown) + label:not(.switch label):not(.lever):not(#history-track-menu label) {
  transform: translateY(calc(-14px / var(--system-scale-factor))) scale(0.8) !important;
  transform-origin: 0 0 !important;
  color: #b3e5fc !important;
}

/* 选择框标签特殊处理 */
.input-field .select-wrapper + label,
.input-field .select-wrapper ~ label,
.input-field:has(.select-wrapper) > label {
  font-size: var(--font-size-md) !important;
  color: #b3e5fc !important;
  font-weight: 400 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  pointer-events: none !important;
  z-index: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
  line-height: 1.2 !important;
  transform: translateY(calc(12px / var(--system-scale-factor))) !important;
  transform-origin: 0% 100% !important;
  transition: transform 0.2s ease-out, color 0.2s ease-out !important;
}

/* 输入字段间距修复 - 防止重叠 */
.input-field {
  margin-bottom: calc(20px / var(--system-scale-factor)) !important;
  min-height: calc(50px / var(--system-scale-factor)) !important;
  padding-top: calc(8px / var(--system-scale-factor)) !important;
}

/* 🔧 强制修复设置菜单间距 - 防止重叠 */
#settings-menu .input-field {
  margin-bottom: calc(40px / var(--system-scale-factor)) !important;
  min-height: calc(70px / var(--system-scale-factor)) !important;
  padding-top: calc(20px / var(--system-scale-factor)) !important;
  padding-bottom: calc(15px / var(--system-scale-factor)) !important;
}

#settings-menu .input-field.col {
  margin-bottom: calc(40px / var(--system-scale-factor)) !important;
  padding-top: calc(20px / var(--system-scale-factor)) !important;
  padding-bottom: calc(15px / var(--system-scale-factor)) !important;
}

#settings-menu .row {
  margin-bottom: calc(30px / var(--system-scale-factor)) !important;
  clear: both !important;
}

/* 排除历史轨道插件的标签 */
#history-track-menu .input-field label,
#history-track-menu label {
  position: static !important;
  font-size: var(--font-size-md) !important;
  color: #ffffff !important;
  margin-bottom: 0 !important;
  margin-right: 12px !important;
  white-space: nowrap !important;
}

/* 🔧 强制修复下拉框样式 */
.dropdown-content {
  background: #001a33 !important;
  border: 1px solid #003366 !important;
  max-height: calc(400px / var(--system-scale-factor)) !important;
  overflow-y: auto !important;
}

.dropdown-content li {
  min-height: calc(48px / var(--system-scale-factor)) !important;
  height: auto !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
  padding: calc(12px / var(--system-scale-factor)) calc(16px / var(--system-scale-factor)) !important;
  color: white !important;
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  display: flex !important;
  align-items: center !important;
}

.dropdown-content li:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.dropdown-content li > a,
.dropdown-content li > span {
  color: white !important;
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
  padding: 0 !important;
  display: block !important;
  width: 100% !important;
}

/* 🔧 强制修复滑动按钮缩放 */
.switch {
  margin-bottom: calc(15px / var(--system-scale-factor)) !important;
}

.switch label {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
  color: white !important;
}

.switch label .lever {
  width: calc(40px / var(--system-scale-factor)) !important;
  height: calc(15px / var(--system-scale-factor)) !important;
  margin: 0 calc(15px / var(--system-scale-factor)) !important;
}

.switch label .lever:after {
  width: calc(21px / var(--system-scale-factor)) !important;
  height: calc(21px / var(--system-scale-factor)) !important;
  top: calc(-3px / var(--system-scale-factor)) !important;
  left: calc(-5px / var(--system-scale-factor)) !important;
}

.switch label input[type=checkbox]:checked + .lever:after {
  left: calc(24px / var(--system-scale-factor)) !important;
}

/* 🔧 强制修复下拉框样式 */
.select-wrapper .dropdown-content {
  max-height: calc(300px / var(--system-scale-factor)) !important;
  min-height: calc(150px / var(--system-scale-factor)) !important;
  overflow-y: auto !important;
}

.select-wrapper .dropdown-content li {
  min-height: calc(50px / var(--system-scale-factor)) !important;
  height: auto !important;
  padding: calc(15px / var(--system-scale-factor)) calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
  display: flex !important;
  align-items: center !important;
}

.select-wrapper .dropdown-content li > span {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
  color: white !important;
}

/* 🔧 强制修复下拉框标签位置 */
.input-field label {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  color: white !important;
  top: calc(8px / var(--system-scale-factor)) !important;
  left: 0 !important;
  transform: none !important;
}

.input-field label.active {
  font-size: calc(12px / var(--system-scale-factor)) !important;
  top: calc(-14px / var(--system-scale-factor)) !important;
  transform: none !important;
}

.input-field .select-dropdown {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
  height: calc(48px / var(--system-scale-factor)) !important;
  padding: calc(14px / var(--system-scale-factor)) 0 !important;
  margin: 0 !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
}

/* 🔧 强制修复提醒文字标签字体缩放 */
.helper-text,
.helper-text[data-error],
.helper-text[data-success] {
  font-size: calc(12px / var(--system-scale-factor)) !important;
  line-height: calc(16px / var(--system-scale-factor)) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  margin-top: calc(8px / var(--system-scale-factor)) !important;
}

/* 数字输入框下方的提醒文字 */
.input-field .helper-text {
  font-size: calc(12px / var(--system-scale-factor)) !important;
  line-height: calc(16px / var(--system-scale-factor)) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  margin-top: calc(8px / var(--system-scale-factor)) !important;
  margin-left: 0 !important;
}

/* 特殊的ECF轨道数量提醒 */
.input-field small,
.input-field .small-text {
  font-size: calc(12px / var(--system-scale-factor)) !important;
  line-height: calc(16px / var(--system-scale-factor)) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  display: block !important;
  margin-top: calc(5px / var(--system-scale-factor)) !important;
}

/* 🔧 强制修复范围滑块样式 */
.input-field input[type=range] {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
  height: calc(48px / var(--system-scale-factor)) !important;
  padding: calc(14px / var(--system-scale-factor)) 0 !important;
  margin: 0 !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  background: transparent !important;
}

/* 范围滑块样式 */
.input-field input[type=range] {
  height: calc(14px / var(--system-scale-factor)) !important;
  margin: calc(20px / var(--system-scale-factor)) 0 !important;
}

/* 🔧 强制修复时间管理器缩放 */
#datetime,
.datetime-container,
.time-machine {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
}

#datetime input,
.datetime-container input,
.time-machine input {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  padding: calc(8px / var(--system-scale-factor)) !important;
}

#datetime button,
.datetime-container button,
.time-machine button {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  padding: calc(8px / var(--system-scale-factor)) calc(12px / var(--system-scale-factor)) !important;
  line-height: calc(16px / var(--system-scale-factor)) !important;
}

/* 🔧 强制修复搜索框缩放 */
#search,
.search-box,
.search-container {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
}

#search input,
.search-box input,
.search-container input {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  height: calc(40px / var(--system-scale-factor)) !important;
  padding: calc(10px / var(--system-scale-factor)) calc(15px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
}

#search .search-results,
.search-box .search-results,
.search-container .search-results {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  line-height: calc(18px / var(--system-scale-factor)) !important;
  max-height: calc(300px / var(--system-scale-factor)) !important;
}

#search .search-results li,
.search-box .search-results li,
.search-container .search-results li {
  padding: calc(10px / var(--system-scale-factor)) calc(15px / var(--system-scale-factor)) !important;
  min-height: calc(40px / var(--system-scale-factor)) !important;
}

/* 🔧 强制修复sat-info-box缩放 */
#sat-info-box,
.sat-info-box {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  line-height: calc(18px / var(--system-scale-factor)) !important;
}

#sat-info-box .sat-info-title,
.sat-info-box .sat-info-title {
  font-size: calc(18px / var(--system-scale-factor)) !important;
  line-height: calc(22px / var(--system-scale-factor)) !important;
  font-weight: bold !important;
}

#sat-info-box .sat-info-content,
.sat-info-box .sat-info-content {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  line-height: calc(18px / var(--system-scale-factor)) !important;
}

#sat-info-box button,
.sat-info-box button {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  height: calc(32px / var(--system-scale-factor)) !important;
  padding: calc(8px / var(--system-scale-factor)) calc(12px / var(--system-scale-factor)) !important;
  line-height: calc(16px / var(--system-scale-factor)) !important;
}

#sat-info-box .close-button,
.sat-info-box .close-button {
  width: calc(24px / var(--system-scale-factor)) !important;
  height: calc(24px / var(--system-scale-factor)) !important;
  font-size: calc(16px / var(--system-scale-factor)) !important;
}

/* 🔥 强制修复侧边栏菜单标题和字体大小 - 更大标题 */
.side-menu h4,
.side-menu h5,
.side-menu .menu-title,
#settings-menu h4,
#settings-menu h5 {
  font-size: calc(28px / var(--system-scale-factor)) !important;
  line-height: calc(34px / var(--system-scale-factor)) !important;
  margin: calc(24px / var(--system-scale-factor)) 0 calc(18px / var(--system-scale-factor)) 0 !important;
  color: white !important;
  font-weight: bold !important;
}

/* 🔥 调大侧边栏整体菜单选项字体 - 使用更大的基础字体 */
.side-menu,
#settings-menu {
  font-size: calc(20px / var(--system-scale-factor)) !important;
  line-height: calc(26px / var(--system-scale-factor)) !important;
}

.side-menu .row .col,
.side-menu .input-field,
.side-menu .switch,
.side-menu label,
.side-menu span,
#settings-menu .row .col,
#settings-menu .input-field,
#settings-menu .switch,
#settings-menu label,
#settings-menu span {
  font-size: calc(20px / var(--system-scale-factor)) !important;
  line-height: calc(26px / var(--system-scale-factor)) !important;
  color: white !important;
}

/* 保持提醒文字相对较小但仍然可读 */
.side-menu .helper-text,
.side-menu small,
#settings-menu .helper-text,
#settings-menu small {
  font-size: calc(14px / var(--system-scale-factor)) !important;
  line-height: calc(18px / var(--system-scale-factor)) !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 🔧 更新下拉框和滑动按钮字体大小 */
.side-menu .select-wrapper .dropdown-content li > span,
#settings-menu .select-wrapper .dropdown-content li > span {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
}

.side-menu .input-field .select-dropdown,
#settings-menu .input-field .select-dropdown {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
}

.side-menu .input-field label,
#settings-menu .input-field label {
  font-size: calc(18px / var(--system-scale-factor)) !important;
}

.side-menu .input-field label.active,
#settings-menu .input-field label.active {
  font-size: calc(18px / var(--system-scale-factor)) !important;
}

/* 🔧 更新滑动按钮标签字体 */
.side-menu .switch label,
#settings-menu .switch label {
  font-size: calc(18px / var(--system-scale-factor)) !important;
  line-height: calc(22px / var(--system-scale-factor)) !important;
}

/* 🔧 更新输入框字体 */
.side-menu .input-field input,
#settings-menu .input-field input {
  font-size: calc(18px / var(--system-scale-factor)) !important;
  line-height: calc(22px / var(--system-scale-factor)) !important;
}

/* 🔧 强制修复所有菜单文字缩放 */
#settings-menu .row .col,
#settings-menu .input-field,
#settings-menu .switch,
#settings-menu label,
#settings-menu span,
#settings-menu .helper-text {
  font-size: calc(16px / var(--system-scale-factor)) !important;
  line-height: calc(20px / var(--system-scale-factor)) !important;
}

#settings-menu .helper-text,
#settings-menu small {
  font-size: calc(12px / var(--system-scale-factor)) !important;
  line-height: calc(16px / var(--system-scale-factor)) !important;
}

/* 确保菜单标题也缩放 */
.side-menu h4,
.side-menu h5,
.side-menu .menu-title {
  font-size: calc(20px / var(--system-scale-factor)) !important;
  line-height: calc(24px / var(--system-scale-factor)) !important;
  margin: calc(20px / var(--system-scale-factor)) 0 calc(15px / var(--system-scale-factor)) 0 !important;
}

/* 让select下拉箭头变为白色 */
select {
  color-scheme: dark;
  background-color: transparent;
  color: white;
}
select::-ms-expand {
  filter: invert(1) brightness(2);
}
select option {
  color: black;
  background: white;
}
select:focus {
  outline: none;
}
select::-webkit-dropdown-arrow {
  filter: invert(1) brightness(2);
}

#history-track-menu input {
  text-align: center;
  vertical-align: baseline;
  line-height: 32px;
}

/* 隐藏原生日期图标 */
input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
  display: none;
}
input[type="date"]::-ms-input-placeholder {
  color: white;
}
input[type="date"]::-moz-placeholder {
  color: white;
}
input[type="date"]::-webkit-input-placeholder {
  color: white;
}
input[type="date"]::placeholder {
  color: white;
}
/* 添加自定义白色SVG日历图标 */
input[type="date"] {
  position: relative;
  background: none;
}
input[type="date"]::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  width: 20px;
  height: 20px;
  transform: translateY(-50%);
  background: url('data:image/svg+xml;utf8,<svg fill="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><rect x="3" y="4" width="18" height="18" rx="2"/><rect x="7" y="10" width="10" height="8" rx="1" fill="black"/></svg>') no-repeat center/contain;
  pointer-events: none;
  z-index: 2;
}