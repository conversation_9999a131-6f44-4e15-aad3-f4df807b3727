/*
 * Unified transparency effects file
 * Replaces: force-transparent.css, transparent-menus.css
 * Consolidates all transparency-related styles to avoid duplication and conflicts
 * Created: 2025-01-25
 */

/* Core transparency variables */
:root {
  --transparency-full: transparent;
  --transparency-light: rgba(0, 0, 0, 0.1);
  --transparency-medium: rgba(0, 0, 0, 0.3);
  --blur-light: blur(5px);
  --blur-medium: blur(10px);
  --blur-heavy: blur(20px);
}

/* Bottom menu transparency effects */
#nav-footer,
#bottom-icons-container {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
  outline: none !important;
}

#bottom-icons {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
}

#bottom-icons-filter {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
}

.bmenu-item,
.bmenu-filter-item {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
  outline: none !important;
}

/* Side menu transparency effects */
[id$="-menu"],
.side-menu,
.menu-container {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
  outline: none !important;
}

/* Menu items - exclude sensor list */
[id$="-menu"]:not(#sensor-list-menu) .menu-item,
.side-menu:not(#sensor-list-content) .menu-item {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
}

/* Menu item hover - exclude sensor list */
[id$="-menu"]:not(#sensor-list-menu) .menu-item:hover,
.side-menu:not(#sensor-list-content) .menu-item:hover {
  background: var(--transparency-light) !important;
  background-color: var(--transparency-light) !important;
}

/* Modal and dialog transparency effects */
.modal,
.dialog,
.popup {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
}

.modal-content,
.dialog-content,
.popup-content {
  background: var(--transparency-medium) !important;
  background-color: var(--transparency-medium) !important;
  border: none !important;
}

/* sat-info-box special handling */
#sat-infobox,
#sat-infobox .modal-content,
#sat-infobox .dialog-content,
#sat-infobox .popup-content {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* sat-info-box hover effects */
html body #sat-infobox .sat-info-row:hover,
body #sat-infobox .sat-info-row:hover,
#sat-infobox .sat-info-row:hover {
  background: rgba(33, 150, 243, 0.3) !important;
  background-color: rgba(33, 150, 243, 0.3) !important;
}

html body #sat-infobox .sat-info-section-header:hover,
body #sat-infobox .sat-info-section-header:hover,
#sat-infobox .sat-info-section-header:hover {
  background: #0d47a1 !important;
  background-color: #0d47a1 !important;
}

/* Search box dedicated styles */
#search {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
  background: transparent !important;
  background-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
}

#search:focus,
#search:active {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
  background: transparent !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

/* Force override: custom sensor menu input-field containers must be borderless */
#custom-sensor-menu .input-field,
#custom-sensor-menu .input-field.col,
#custom-sensor-menu .input-field.col.s12,
div#custom-sensor-menu .input-field,
div#custom-sensor-menu .input-field.col,
div#custom-sensor-menu .input-field.col.s12 {
  border: none !important;
  border-color: transparent !important;
}

/* Force override: satellite FOV menu input-field containers must be borderless */
#satellite-fov-menu .input-field,
#satellite-fov-menu .input-field.col,
#satellite-fov-menu .input-field.col.s12,
div#satellite-fov-menu .input-field,
div#satellite-fov-menu .input-field.col,
div#satellite-fov-menu .input-field.col.s12,
#satellite-fov-menu form .input-field,
#satellite-fov-menu form .input-field.col,
#satellite-fov-menu form .input-field.col.s12 {
  border: none !important;
  border-color: transparent !important;
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
}

/* Input field bottom border restoration */
.side-menu input[type="text"],
.side-menu input[type="number"],
.side-menu input[type="email"],
.side-menu input[type="password"],
.side-menu input[type="date"],
.side-menu input[type="time"],
.side-menu input[type="datetime-local"],
.side-menu input[type="search"],
.side-menu input[type="url"],
.side-menu input[type="tel"],
[id$="-menu"] input[type="text"],
[id$="-menu"] input[type="number"],
[id$="-menu"] input[type="email"],
[id$="-menu"] input[type="password"],
[id$="-menu"] input[type="date"],
[id$="-menu"] input[type="time"],
[id$="-menu"] input[type="datetime-local"],
[id$="-menu"] input[type="search"],
[id$="-menu"] input[type="url"],
[id$="-menu"] input[type="tel"] {
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 0 !important;
  background: transparent !important;
  background-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Force transparency classes */
.force-transparent {
  background: var(--transparency-full) !important;
  background-color: var(--transparency-full) !important;
  border: none !important;
  outline: none !important;
}

.force-transparent-light {
  background: var(--transparency-light) !important;
  background-color: var(--transparency-light) !important;
}

.force-transparent-medium {
  background: var(--transparency-medium) !important;
  background-color: var(--transparency-medium) !important;
}

/* Disable transparency class */
.disable-transparency {
  background: #1a1a1a !important;
  background-color: #1a1a1a !important;
  border: 1px solid #333 !important;
}

/* Responsive transparency effects */
@media (max-width: 768px) {
  :root {
    --blur-light: blur(2px);
    --blur-medium: blur(3px);
    --blur-heavy: blur(5px);
  }
}

@media (min-width: 1920px) and (min-resolution: 2dppx) {
  :root {
    --blur-light: blur(8px);
    --blur-medium: blur(15px);
    --blur-heavy: blur(30px);
  }
}

/* History longitude page dedicated transparency styles */
#geo-longitude-history-menu input,
#geo-longitude-history-menu input[type="text"],
#geo-longitude-history-menu input[type="date"] {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
  border-radius: 0 !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
}

#geo-longitude-history-menu input:focus,
#geo-longitude-history-menu input[type="text"]:focus,
#geo-longitude-history-menu input[type="date"]:focus {
  background: transparent !important;
  background-color: transparent !important;
  border-bottom-color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
}
